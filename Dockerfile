FROM openjdk:11
ARG JAR_FILE=target/*.jar
ADD src/main/resources/azvasa.online.jks /etc/ssl/certs/azvasa.online.jks
COPY ${JAR_FILE} notification-service-2.2.0.jar
ENTRYPOINT ["java","-Djava.security.egd=file:/dev/./urandom","-Djavax.net.ssl.trustStore=/etc/ssl/certs/azvasa.online.jks","-Djavax.net.ssl.trustStorePassword=azvasa123","-Dcom.amazonaws.sdk.disableCertChecking=true","-jar","notification-service-2.2.0.jar"]
