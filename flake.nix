{
  description = "Development environment with OpenJDK 21 for multi-service project";

  inputs = {
    nixpkgs.url = "github:NixOS/nixpkgs/nixos-unstable";
    flake-utils.url = "github:numtide/flake-utils";
  };

  outputs = { self, nixpkgs, flake-utils }:
    flake-utils.lib.eachDefaultSystem (system:
      let
        pkgs = nixpkgs.legacyPackages.${system};
      in
      {
        devShells.default = pkgs.mkShell {
          buildInputs = with pkgs; [
            # Java Development
            openjdk11
            maven
            gradle

            # Node.js Development (for the web projects)
            nodejs_20
          ];

          shellHook = ''
            echo "🚀 Development environment loaded!"
            echo "📦 Available tools:"
            echo "  - Java: $(java -version 2>&1 | head -n 1)"
            echo "  - Maven: $(mvn -version | head -n 1)"
            echo "  - Node.js: $(node --version)"
            echo "  - npm: $(npm --version)"
            echo ""
            echo "💡 To get started:"
            echo "  - Java projects: Use 'mvn clean install' in project directories"
            echo "  - Node.js projects: Use 'npm install' in project directories"
            echo ""
            
            # Set JAVA_HOME for consistency
            export JAVA_HOME="${pkgs.openjdk11}/lib/openjdk"
            export PATH="$JAVA_HOME/bin:$PATH"
            
            # Set Maven options for better performance
            export MAVEN_OPTS="-Xmx2g -XX:ReservedCodeCacheSize=512m"
          '';

          # Environment variables
          JAVA_HOME = "${pkgs.openjdk11}/lib/openjdk";
          MAVEN_HOME = "${pkgs.maven}";
        };

        # Optional: You can also define specific shells for different purposes
        devShells.java-only = pkgs.mkShell {
          buildInputs = with pkgs; [
            openjdk11
            maven
            gradle
          ];

          shellHook = ''
            echo "☕ Java-only development environment"
            echo "Java: $(java -version 2>&1 | head -n 1)"
            export JAVA_HOME="${pkgs.openjdk11}/lib/openjdk"
          '';
        };

        devShells.node-only = pkgs.mkShell {
          buildInputs = with pkgs; [
            nodejs_20
          ];
          
          shellHook = ''
            echo "🟢 Node.js-only development environment"
            echo "Node.js: $(node --version)"
          '';
        };
      }
    );
}
