package com.lms.notification.enums;

/**
 * Source type defines the type or category of notifications from different
 * services
 *
 * <AUTHOR> SAHU
 * @since 2.0.0
 *
 */
public enum ResourceType {

    CONCEPT("CONCEPT", "Concept");

    private String code;

    private String name;

    ResourceType(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
