package com.lms.notification.enums;

public enum SmsAction {

	USER_CREATION("User Creation", "USER_CREATION"), UPDATE_PASSWORD("Update Password", "UPDATE_PASSWORD"),
	SHARE_ID("Share id", "SHARE_ID"), QUIZ_RELEASE("Quiz Release", "QUIZ_RELEASE"),
	QUIZ_RESULT("Quiz Result", "QUIZ_RESULT"), UPDATE_PROFILE("Edit & Update Profile", "UPDATE_PROFILE");

	private String code;

	private String name;

	SmsAction(String name, String code) {
		this.name = name;
		this.code = code;
	}

	public String getCode() {
		return code;
	}

	public String getName() {
		return name;
	}

}
