package com.lms.notification.util;

import com.lms.notification.enums.ErrorCodes;
import com.lms.notification.exception.NSException;
import com.lms.notification.model.LMSResponse;

/**
 * Create the common response format for all the API
 *
 * <AUTHOR> C Achari
 * @since 0.0.1
 *
 */
public class ResponseHelper {

    /**
     * Use when the API return some response class.
     *
     * @param response
     * @param data
     * @param successMessage
     * @param errorMessage
     * @return
     */
    public static LMSResponse createResponse(LMSResponse response, Object data, String successMessage,
            String errorMessage) {

        if (data != null) {
            response.setSuccess(true);
            response.setData(data);
            response.setMessage(successMessage);
        } else {
            throw new NSException(ErrorCodes.INTERNAL_SERVER_ERROR, errorMessage);
        }
        return response;
    }

    /**
     * Use this format when the API returns only boolean
     *
     * @param response
     * @param flag
     * @param successMessage
     * @param errorMessage
     * @return
     */
    public static LMSResponse createResponseForFlags(LMSResponse response, boolean flag, String successMessage,
            String errorMessage) {
        if (flag) {
            response.setSuccess(flag);
            response.setMessage(successMessage);
        } else {
            throw new NSException(ErrorCodes.INTERNAL_SERVER_ERROR, errorMessage);
        }
        return response;
    }
}
