package com.lms.notification.util;

import java.util.Arrays;
import java.util.List;

import org.apache.commons.lang3.exception.ExceptionUtils;

import com.lms.notification.component.Translator;
import com.lms.notification.enums.ErrorCodes;
import com.lms.notification.exception.NSException;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class FieldMappers {

	@SuppressWarnings("all")
	public static String smsConfigApiFieldMapper(String value) {
		try {
			String[] requestFields = { "id", "smsAction", "template", "createdAt", "active" };
			List<String> apiFileds = Arrays.asList(requestFields);

			String[] queryFields = { "id", "smsAction", "template", "createdAt", "active" };
			List<String> dbFileds = Arrays.asList(queryFields);

			int index = apiFileds.indexOf(value);
			return (index <= 0) ? dbFileds.get(0) : dbFileds.get(index);
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new NSException(ErrorCodes.NOT_FOUND, Translator.toLocale("field.not.exist", null));
		}
	}

}
