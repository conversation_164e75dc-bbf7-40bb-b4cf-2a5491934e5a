package com.lms.notification.response.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class NotificationResponseDto {

    @ApiModelProperty(example = "NotifyTo Id", position = 1)
    private String id;
    
    @ApiModelProperty(example = "Concept approval pending", position = 2)
    private String title;

    @ApiModelProperty(example = "water and electricity", position = 3)
    private String description;

    @ApiModelProperty(example = "/v1/concept/pagination", position = 4)
    private String url;
    
    @ApiModelProperty(example = "abc.jpg", position = 5)
    private String thumbnail;

    @ApiModelProperty(example = "false", position = 6)
    private boolean read;

    @ApiModelProperty(example = "1662626086017", position = 7)
    private Long createdAt;

}
