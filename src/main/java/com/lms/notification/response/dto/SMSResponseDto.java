package com.lms.notification.response.dto;

import com.lms.notification.Projection.SMSProjection;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SMSResponseDto {

	@ApiModelProperty(value = "id of the sms action", example = "2c91808483cf75800183cfb4e5500000", position = 1)
	private String id;

	@ApiModelProperty(value = "sms action", example = "USER_CREATION", position = 2)
	private String smsAction;
	
	@ApiModelProperty(value = "sms action", example = "User Creation", position = 3)
	private String smsActionName;

	@ApiModelProperty(value = "id of the sms action", example = "2c91808483cf75800183cfb4e5500000", position = 4)
	private String template;

	@ApiModelProperty(value = "active", example = "false", position = 5)
	private boolean active;

	public SMSResponseDto(SMSProjection projection) {
		this.id = projection.getId();
		this.smsAction = projection.getSmsAction();
		this.smsActionName = projection.getSmsActionName();
		this.template = projection.getTemplate();
		this.active = projection.isActive();
	}

}
