package com.lms.notification.config;

import java.util.HashMap;
import java.util.Map;

import org.apache.kafka.clients.admin.NewTopic;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.kafka.config.TopicBuilder;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.ProducerFactory;

import com.lms.notification.request.dto.CreateUserRequestDto;

//@EnableKafka
//@Configuration
public class KafkaConfig {

    @Value("${spring.kafka.bootstrap-servers}")
    private String kafkaUrl;

    @Value("${app.kafka.producer.topic}")
    private String emailTopic;

    @Value("${app.kafka.producer.partition}")
    private int partition;

    @Value("${app.kafka.producer.replicas}")
    private int replica;

    @Autowired
    private ProducerFactory<String, Object> producerFactory;

    public Map<String, Object> producerConfig() {
        Map<String, Object> producerConfig = new HashMap<>(producerFactory.getConfigurationProperties());
        return producerConfig;
    }

    @Bean
    public NewTopic compactTopicExample() {
        return TopicBuilder.name(emailTopic)
                .partitions(partition)
                .replicas(replica)
                .build();
    }

    @Bean
    public KafkaTemplate<String, CreateUserRequestDto> kafkaCreateOrderTemplate() {
        return new KafkaTemplate<>(new DefaultKafkaProducerFactory<>(producerConfig()));
    }

}
