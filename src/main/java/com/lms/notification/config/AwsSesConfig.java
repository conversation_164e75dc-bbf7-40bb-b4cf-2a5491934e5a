package com.lms.notification.config;

import org.springframework.context.annotation.Configuration;

import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.ses.SesClient;
import software.amazon.awssdk.services.ses.model.SendTemplatedEmailRequest;
import software.amazon.awssdk.services.ses.model.SendTemplatedEmailResponse;
import software.amazon.awssdk.core.client.config.ClientOverrideConfiguration;
import software.amazon.awssdk.http.urlconnection.UrlConnectionHttpClient;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import java.net.URI;

@Configuration
public class AwsSesConfig {

	@Value("${aws.accessKey}")
	private String accessKey;

	@Value("${aws.secretKey}")
	private String secretKey;

	@Value("${aws.region}")
	private String region;

	@Value("${aws.ses.endpoint:}")
	private String sesEndpoint;

	@Bean
	public SesClient sesClient() {
		SesClient.Builder builder = SesClient.builder()
				.region(Region.of(region))
				.credentialsProvider(StaticCredentialsProvider.create(AwsBasicCredentials.create(accessKey, secretKey)));

		// Configure for LocalStack if endpoint is provided
		if (sesEndpoint != null && !sesEndpoint.isEmpty()) {
			builder.endpointOverride(URI.create(sesEndpoint))
				   .httpClient(UrlConnectionHttpClient.builder().build());
		}

		return builder.build();
	}

	
}
