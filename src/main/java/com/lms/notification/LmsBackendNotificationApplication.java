package com.lms.notification;

import org.modelmapper.ModelMapper;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.loadbalancer.LoadBalanced;
//import org.springframework.cloud.netflix.eureka.EnableEurekaClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.web.client.RestTemplate;

/**
 * <AUTHOR> The Notification Service
 */
@SpringBootApplication (
	exclude = {
            org.springframework.cloud.aws.autoconfigure.context.ContextInstanceDataAutoConfiguration.class,
            org.springframework.cloud.aws.autoconfigure.context.ContextStackAutoConfiguration.class,
            org.springframework.cloud.aws.autoconfigure.context.ContextRegionProviderAutoConfiguration.class
    }
)
//@EnableEurekaClient
@EnableFeignClients
//@EnableKafka
public class LmsBackendNotificationApplication {

    /**
     * <AUTHOR> Run Main Application
     */
    public static void main(String[] args) {
        SpringApplication.run(LmsBackendNotificationApplication.class, args);
        System.setProperty("javax.net.ssl.trustStore", "/etc/ssl/certs/azvasa.online.jks");
        System.setProperty("javax.net.ssl.trustStorePassword", "azvasa123");
    
    }

    @Bean
    @LoadBalanced
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }

    /**
     * The Model Mapper method
     *
     * @return mapping of Objects
     */
    @Bean
    public ModelMapper modelMapper() {
        ModelMapper modelMapper = new ModelMapper();
        modelMapper.getConfiguration().setAmbiguityIgnored(true);
        return modelMapper;
    }

}
