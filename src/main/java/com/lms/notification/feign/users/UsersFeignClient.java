package com.lms.notification.feign.users;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

import com.lms.notification.config.FiegnConfiguration;
import com.lms.notification.model.LMSResponse;
import org.springframework.web.bind.annotation.RequestHeader;

/**
 * Call the API's from the service USERS-SERVICE, This interface only designed
 * for the USERS-SERVICE.
 *
 * <AUTHOR> <PERSON>
 * @since 1.0.0
 *
 */
@FeignClient(name = "USER-SERVICE", configuration = FiegnConfiguration.class, url="${api.user.service.url}")
public interface UsersFeignClient {

    @GetMapping("/v1/api/user/users/username/{username}")
    public LMSResponse<UsersFeignDto> getUsersByUsernameForFeign(@PathVariable("username") String username);

    @GetMapping("/v1/api/user/users/username/{username}")
    public LMSResponse<UsersFeignDto> getUsersByUsernameForFeign(@PathVariable("username") String username, @RequestHeader("Authorization") String bearerToken);
}
