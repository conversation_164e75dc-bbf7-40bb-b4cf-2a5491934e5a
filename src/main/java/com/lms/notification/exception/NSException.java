package com.lms.notification.exception;

import com.lms.notification.enums.ErrorCodes;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * <AUTHOR>
 * @modifiedOn 14-Mar-2022
 * @since 0.0.1
 *
 */
@Getter
@Setter
public class NSException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    private Integer errorCode;

    private String message;

//	public UserServiceException(String message, Throwable cause) {
//		super(message, cause);
//	}
//	public UserServiceException(String list) {
//		super(list);
//	}
//	public UserServiceException(Throwable cause) {
//		super(cause);
//	}
    public NSException(String message) {
        super(message);
        this.message = message;
    }

    public NSException(Integer errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
        this.message = message;
    }

    public NSException(ErrorCodes errorCode, String message) {
        super(message);
        this.errorCode = errorCode.getCode();
        this.message = message;
    }

    public NSException(Integer errorCode, Exception ex) {
        super(ex);
        this.errorCode = errorCode;
        this.message = ex.getMessage();
    }

    public NSException(Exception ex) {
        super(ex);
        this.message = ex.getMessage();
    }

}
