package com.lms.notification.service;

import com.lms.notification.model.PaginatedResponse;
import com.lms.notification.request.dto.SMSRequestDto;
import com.lms.notification.request.dto.SmsGatewayRequestDto;
import com.lms.notification.response.dto.SMSResponseDto;

public interface SMSConfigurationService {

	/**
	 * This api is used for to Create or Update the SMS Configuration and Template.
	 * 
	 * @param request
	 * @return
	 */
	SMSResponseDto createOrUpdateSMSConfiguration(SMSRequestDto request);

	Boolean updateActiveField(String id, boolean active);

	/**
	 * Delete SMS Configuration (mark Branch as Deleted)
	 *
	 * @param smsConfigurationId The SMS Configuration ID
	 * @return boolean
	 */
	Boolean removeSMSConfiguration(String smsConfigurationId);

	/**
	 * This API is used for get SMS Configuration by Id or SMS Action.
	 * 
	 * @param id
	 * @param smsAction
	 * @return
	 */
	SMSResponseDto getSMSConfigurationByIdOrSmsAction(String id, String smsAction);

	/**
	 * This API is used for get all SMS configurations.
	 * 
	 * @param pageNumber
	 * @param pageSize
	 * @param sortBy
	 * @param order
	 * @param search
	 * @param id
	 * @param smsAction
	 * @param active
	 * @return
	 */
	PaginatedResponse<SMSResponseDto> getAllSMSConfigurationsByPage(int pageNumber, int pageSize, String sortBy,
			boolean order, String search, String id, String smsAction, Boolean active);

	/**
	 * This API is used for send SMS to user USER_CREATION, UPDATE_PASSWORD,
	 * SHARE_ID, QUIZ_RELEASE, QUIZ_RESULT, and UPDATE_PROFILE.
	 * 
	 * @param request
	 * @return
	 */
	Boolean sendSMSToUser(SmsGatewayRequestDto request);



}
