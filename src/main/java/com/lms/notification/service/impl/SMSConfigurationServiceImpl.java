package com.lms.notification.service.impl;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.net.URLConnection;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.net.URL;
import java.net.URLConnection;
import java.net.URLEncoder;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.modelmapper.ModelMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import com.google.common.base.Enums;
import com.lms.notification.Projection.SMSProjection;
import com.lms.notification.authentication.JwtUtil;
import com.lms.notification.component.Translator;
import com.lms.notification.entity.SMSConfiguration;
import com.lms.notification.enums.ErrorCodes;
import com.lms.notification.enums.SmsAction;
import com.lms.notification.exception.NSException;
import com.lms.notification.model.PaginatedResponse;
import com.lms.notification.repository.SMSConfigurationRepository;
import com.lms.notification.request.dto.BatchReceptionistRequestDto;
import com.lms.notification.request.dto.SMSRequestDto;
import com.lms.notification.request.dto.SmsGatewayRequestDto;
import com.lms.notification.response.dto.SMSResponseDto;
import com.lms.notification.service.SMSConfigurationService;
import com.lms.notification.util.FieldMappers;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class SMSConfigurationServiceImpl implements SMSConfigurationService {

	@Autowired
	private JwtUtil jwtUtil;

	@Value("${sms.gateway.url}")
	private String url;

	@Value("${sms.gateway.username}")
	private String userName;

	@Value("${sms.gateway.password}")
	private String password;

	@Value("${sms.gateway.sender}")
	private String sender;

	@Value("${sms.gateway.type}")
	private String type;

	@Autowired
	private SMSConfigurationRepository smsConfigurationRepo;

	@Autowired
	private ModelMapper modelMapper;

	/**
	 * This api is used for to Create or Update the SMS Configuration and Template.
	 * 
	 * @param request
	 * @return
	 */
	@Override
	public SMSResponseDto createOrUpdateSMSConfiguration(SMSRequestDto request) {
		try {

			if (!Enums.getIfPresent(SmsAction.class, request.getSmsAction()).isPresent())
				throw new NSException(ErrorCodes.BAD_REQUEST, Translator.toLocale("sms.action.invalid.data", null));

			if (!StringUtils.isEmpty(request.getId())
					&& !smsConfigurationRepo.existsByIdAndDeleted(request.getId(), false))
				throw new NSException(ErrorCodes.NOT_FOUND, Translator.toLocale("sms.configuration.not.found", null));

			if (StringUtils.isEmpty(request.getId())) {
				if (smsConfigurationRepo.existsBySmsActionAndDeleted(SmsAction.valueOf(request.getSmsAction()), false))
					throw new NSException(ErrorCodes.BAD_REQUEST, Translator.toLocale("sms.action.exist", null));

				if (smsConfigurationRepo.existsByTemplateAndDeleted(request.getTemplate(), false))
					throw new NSException(ErrorCodes.BAD_REQUEST, Translator.toLocale("sms.template.exist", null));
			}

			String currentUser = jwtUtil.currentLoginUser();
			Long currentTime = new Date().getTime();
			SMSConfiguration smsConfig = !StringUtils.isEmpty(request.getId())
					? smsConfigurationRepo.getById(request.getId())
					: modelMapper.map(request, SMSConfiguration.class);

			if (!StringUtils.isEmpty(request.getId())) {
				smsConfig = modelMapper.map(request, SMSConfiguration.class);
				smsConfig.setLastModifiedBy(currentUser);
				smsConfig.setModifiedAt(currentTime);
			} else
				smsConfig.setCreatedBy(currentUser);

			smsConfig.setSmsAction(SmsAction.valueOf(request.getSmsAction()));
			smsConfig.setSmsActionName(SmsAction.valueOf(request.getSmsAction()).getName());
			smsConfig = smsConfigurationRepo.save(smsConfig);

			return new SMSResponseDto(smsConfig.getId(), smsConfig.getSmsAction().getCode(),
					smsConfig.getSmsActionName(), smsConfig.getTemplate(), smsConfig.isActive());
		} catch (NSException e) {
			log.info(ExceptionUtils.getStackTrace(e));
			throw new NSException(e.getErrorCode(), e.getMessage());
		} catch (Exception e) {
			log.error(ExceptionUtils.getMessage(e));
			throw new NSException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("sms.configuration.create.failed", null));
		}
	}

	/**
	 * This API is used for toggle the SMS Configuration.
	 */
	@Override
	public Boolean updateActiveField(String id, boolean active) {
		try {
			if (!smsConfigurationRepo.existsById(id))
				throw new NSException(ErrorCodes.NOT_FOUND, Translator.toLocale("sms.configuration.not.found", null));

			if (!smsConfigurationRepo.existsById(id))
				throw new NSException(ErrorCodes.NOT_FOUND, Translator.toLocale("sms.configuration.not.found", null));

			SMSConfiguration smsConfig = smsConfigurationRepo.getById(id);
			Long currentTime = new Date().getTime();
			smsConfig.setActive(active);
			smsConfig.setModifiedAt(currentTime);
			smsConfig.setLastModifiedBy(jwtUtil.currentLoginUser());
			smsConfig = smsConfigurationRepo.save(smsConfig);
			return smsConfig.isActive() == active;
		} catch (NSException us) {
			log.error(ExceptionUtils.getStackTrace(us));
			throw new NSException(us.getErrorCode(), us.getMessage());
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new NSException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("active.update.failed", null));
		}
	}

	/**
	 * Delete SMS Configuration (mark SMS Configuration as Deleted)
	 *
	 * @param smsConfigurationId The SMS Configuration ID
	 * @return boolean
	 */
	@Override
	public Boolean removeSMSConfiguration(String smsConfigurationId) {
		try {
			if (!smsConfigurationRepo.existsById(smsConfigurationId))
				throw new NSException(ErrorCodes.NOT_FOUND, Translator.toLocale("sms.configuration.not.found", null));

			String currentUser = jwtUtil.currentLoginUser();
			Long currentTime = new Date().getTime();
			SMSConfiguration smsConfig = smsConfigurationRepo.getById(smsConfigurationId);
			smsConfig.setModifiedAt(currentTime);
			smsConfig.setDeleted(true);
			smsConfig.setActive(false);
			smsConfig.setLastModifiedBy(currentUser);
			smsConfig = smsConfigurationRepo.save(smsConfig);
			if (smsConfig.isDeleted())
				log.info("SMS Configuration marked as deleted");

			return smsConfig.isDeleted();
		} catch (NSException us) {
			log.error(ExceptionUtils.getStackTrace(us));
			throw new NSException(us.getErrorCode(), us.getMessage());
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new NSException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("sms.configuration.delete.failed", null));
		}
	}

	/**
	 * This API is used for get SMS Configuration by Id or SMS Action.
	 * 
	 * @param id
	 * @param smsAction
	 * @return
	 */
	@Override
	public SMSResponseDto getSMSConfigurationByIdOrSmsAction(String id, String smsAction) {
		log.info("SMS Configuration Details fetch started...");
		try {
			if (StringUtils.isEmpty(id) && StringUtils.isEmpty(smsAction))
				throw new NSException(ErrorCodes.BAD_REQUEST,
						Translator.toLocale("sms.configuration.request.parameter", null));

			if (!StringUtils.isEmpty(id) && !smsConfigurationRepo.existsById(id))
				throw new NSException(ErrorCodes.NOT_FOUND, Translator.toLocale("sms.configuration.not.found", null));

			if (!StringUtils.isEmpty(smsAction)
					&& !smsConfigurationRepo.existsBySmsActionAndDeleted(SmsAction.valueOf(smsAction), false))
				throw new NSException(ErrorCodes.NOT_FOUND, Translator.toLocale("sms.configuration.not.found", null));

			SMSProjection projection = smsConfigurationRepo.findSMSConfigurationByIdOrSmsAction(id, smsAction);
			if (projection != null)
				log.info("GET Detail SMS Configuration fetched successfully");
			return projection != null ? new SMSResponseDto(projection) : null;
		} catch (NSException us) {
			log.error(ExceptionUtils.getStackTrace(us));
			throw new NSException(us.getErrorCode(), us.getMessage());
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new NSException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("sms.configuration.get.by.id.sms.action.failed", null));
		}
	}

	/**
	 * This API is used for get all SMS configurations.
	 * 
	 * @param pageNumber
	 * @param pageSize
	 * @param sortBy
	 * @param order
	 * @param search
	 * @param id
	 * @param smsAction
	 * @param template
	 * @param active
	 * @return
	 */
	@Override
	public PaginatedResponse<SMSResponseDto> getAllSMSConfigurationsByPage(int pageNumber, int pageSize, String sortBy,
			boolean order, String search, String id, String smsAction, Boolean active) {
		log.info("SMS Configuration pagination fetch started...");
		try {
			Long totalElements = 0L;
			Integer totalPages = 0;
			log.info("SMS Configuration pagination in progress...");
			String sortField = FieldMappers.smsConfigApiFieldMapper(sortBy);
			String searchFormat = (!StringUtils.isEmpty(search)) ? search.toLowerCase() : null;

			List<SMSResponseDto> response = new ArrayList<>();
			Pageable pageable = PageRequest.of(pageNumber, pageSize,
					Sort.by(order ? Direction.ASC : Direction.DESC, sortField));
			Page<SMSProjection> pagedSMSConfig = smsConfigurationRepo.findAllSMSConfigurationByPagination(id, smsAction,
					searchFormat, pageable, active);

			if (!CollectionUtils.isEmpty(pagedSMSConfig.getContent())) {
				log.info("SMS Configuration details listed, and setting to the response.");
				totalElements = pagedSMSConfig.getTotalElements();
				totalPages = pagedSMSConfig.getTotalPages();
				response = pagedSMSConfig.getContent().stream().map(SMSResponseDto::new).collect(Collectors.toList());
			}
			return new PaginatedResponse<>(totalElements, totalPages, pageSize, (pageNumber + 1), response.size(),
					response);
		} catch (NSException us) {
			log.error(ExceptionUtils.getStackTrace(us));
			throw new NSException(us.getErrorCode(), us.getMessage());
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new NSException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("sms.configuration.get.all.failed", null));
		}
	}

	/**
	 * This API is used for send SMS to user USER_CREATION, UPDATE_PASSWORD,
	 * SHARE_ID, QUIZ_RELEASE, QUIZ_RESULT, and UPDATE_PROFILE.
	 * 
	 * @param request
	 * @return
	 */
	@Override
	public Boolean sendSMSToUser(SmsGatewayRequestDto request) {
		try {
			SMSProjection projection = smsConfigurationRepo.getBySMSAction(request.getSmsAction());
			String template = projection != null ? projection.getTemplate() : null;
			String approvedTemplateId = projection != null ? projection.getApprovedTemplateId() : null;

			// Trigger sending SMS if there are any approved template Id from DLT.
			if (!StringUtils.isEmpty(approvedTemplateId)) {
				// Handling batch operations
				if (request.isBatchOperation() && !CollectionUtils.isEmpty(request.getRecipientList())) {
					if (SmsAction.QUIZ_RELEASE.getCode().equals(request.getSmsAction())) {
						List<BatchReceptionistRequestDto> recipients = request.getRecipientList();

						for (BatchReceptionistRequestDto recipient : recipients) {
							// Replace placeholder in the template with receptionist's data
							String updatedTemplate = template.replace("firstName", recipient.getFirstName())
									.replace("startDate", request.getStartDate())
									.replace("startTime", request.getStartTime())
									.replace("chapter", request.getChapter());
							// Store the updated template in the map with the phone number as the key
							sendSMS(recipient.getPhoneNumber(), updatedTemplate, approvedTemplateId);
						}
						return true;
					}
				} else {
					if (!StringUtils.isBlank(request.getFirstName()))
						template = template.replace("firstName", request.getFirstName());

					if (!StringUtils.isBlank(request.getUserName()))
						template = template.replace("userName", request.getUserName());

					if (!StringUtils.isBlank(request.getPhoneNumber()))
						template = template.replace("phoneNumber", request.getPhoneNumber());

					if (!StringUtils.isBlank(request.getEmailId()))
						template = template.replace("emailId", request.getEmailId());

					if (!StringUtils.isBlank(request.getLastModifiedAt()))
						template = template.replace("lastModifiedAt", request.getLastModifiedAt());

					if (!StringUtils.isBlank(request.getChapter()))
						template = template.replace("chapter", request.getChapter());

					if (!StringUtils.isBlank(request.getQuizName()))
						template = template.replace("quizName", request.getQuizName());

					if (request.getTotalMarks() != null)
						template = template.replace("totalMarks", String.valueOf(request.getTotalMarks()));

					if (!StringUtils.isBlank(request.getStartDate()))
						template = template.replace("startDate", request.getStartDate());

					if (!StringUtils.isBlank(request.getStartTime()))
						template = template.replace("startTime", request.getStartTime());

					if (request.getTotalObtainedMark() != null)
						template = template.replace("totalObtainedMark",
								String.valueOf(request.getTotalObtainedMark()));

					sendSMS(request.getPhoneNumber(), template, approvedTemplateId);
					return true;
				}
			} else
				log.warn("Sorry there is no approved template for " + request.getSmsAction() + "!");

			return false;
		} catch (NSException us) {
			log.error(ExceptionUtils.getStackTrace(us));
			throw new NSException(us.getErrorCode(), us.getMessage());
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new NSException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("sms.send.user.failed", null));
		}
	}

	private void sendSMS(String phoneNumber, String template, String approvedTemplateId) {
// 		try {
// 			RestTemplate restTemplate = new RestTemplate();
// 			StringBuilder urlData = new StringBuilder();
// //			String encoding = "UTF-8";
// 			urlData.append("user=" + userName);
// //			urlData.append("user=" + URLEncoder.encode(userName, encoding));
// 			urlData.append("&password=" + password);
// 			urlData.append("&mobile=" + phoneNumber);
// 			urlData.append("&message=" + template);
// 			urlData.append("&sender=" + sender);
// 			urlData.append("&type=" + type);
// 			urlData.append("&template_id=" + approvedTemplateId);

// 			restTemplate.getForObject(url + urlData.toString(), Object.class);

// 		} catch (Exception e) {
// 			log.error(ExceptionUtils.getStackTrace(e));
// 			throw new NSException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("sms.send.user.failed", null));
// 		}


		try {
			// Construct data
			String data = "user=" + URLEncoder.encode("Azvasa", "UTF-8");
			data += "&password=" + URLEncoder.encode("Azvasa@123", "UTF-8");
			data += "&message=" + URLEncoder.encode(template,  "UTF-8");
			data += "&sender=" + URLEncoder.encode("AZVASA", "UTF-8");
			data += "&mobile=" + URLEncoder.encode(phoneNumber, "UTF-8");
			data += "&type=" + URLEncoder.encode("3", "UTF-8");
			data += "&template_id=" + URLEncoder.encode("1007951310759545888", "UTF-8");
			// Send data
			URL url = new URL("http://api.bulksmsgateway.in/sendmessage.php?" + data);
			URLConnection conn = url.openConnection();
			conn.setDoOutput(true);
			OutputStreamWriter wr = new OutputStreamWriter(conn.getOutputStream());
			wr.write(data);
			wr.flush();
			// Get the response
			BufferedReader rd = new BufferedReader(new InputStreamReader(conn.getInputStream()));
			String line;
			String sResult1 = "";
			while ((line = rd.readLine()) != null) {
				// Process line...
				sResult1 = sResult1 + line + " ";
			}
			wr.close();
			rd.close();
			log.info(sResult1);

			
		} catch (Exception e) {
			e.printStackTrace();
			log.error(e.getMessage());
			
		}
	}

}
