package com.lms.notification.service.impl;

import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.lms.notification.constants.SmsConstants;
import com.lms.notification.entity.SmsAlertBody;
import com.lms.notification.enums.ErrorCodes;
import com.lms.notification.exception.NSException;
import com.lms.notification.response.dto.SmsSendResponseDTO;
import com.lms.notification.service.SMSAlertService;
import com.lms.notification.util.SMSUtils;
import com.twilio.rest.api.v2010.account.Message;
import com.twilio.type.PhoneNumber;

/**
 * <AUTHOR> Sheikh
 * @since 24-Mar-2022
 *
 */
@Service
public class SMSAlertServiceImpl implements SMSAlertService {

    private static final Logger log = LoggerFactory.getLogger(SMSAlertServiceImpl.class);

    @Value("${twilio.phone-number}")
    private String twilioPhoneNumber;
    
//    @Autowired
//	private SMSUtils smsUtils;

    @Override
    public SmsAlertBody sendAlertSms(SmsAlertBody smsAlertBody) {
        try {
            log.info(" Sending SMS ");
            Message.creator(new PhoneNumber(smsAlertBody.getPhoneNumber()), new PhoneNumber(twilioPhoneNumber),
                    smsAlertBody.getMessage()).create();
            log.info("SMS Sent....");
            return smsAlertBody;
        } catch (Exception e) {
            log.error(ExceptionUtils.getStackTrace(e));
            throw new NSException(ErrorCodes.INTERNAL_SERVER_ERROR, SmsConstants.SMS_SEND_FAIL);

        }

    }

	@SuppressWarnings("all")
	@Override
	public String sendSmsUser(SmsSendResponseDTO smsSendResponseDTO) {
		String smsresponse = "";
		try {
			smsresponse = SMSUtils.sendSms1(smsSendResponseDTO);
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new NSException(ErrorCodes.INTERNAL_SERVER_ERROR, SmsConstants.SMS_SEND_FAIL);

		}
		return smsresponse;
	}
}
