package com.lms.notification.service.impl;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.net.URI;
import java.net.URL;
import java.net.URLConnection;
import java.net.URLEncoder;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.util.HashMap;
import java.util.Map;

import javax.mail.MessagingException;
import javax.mail.internet.MimeMessage;

import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lms.notification.constants.EmailConstants;
import com.lms.notification.dto.EmailDto;
import com.lms.notification.service.EmailService;
import com.sendgrid.SendGrid;
import com.sendgrid.helpers.mail.Mail;
import com.sendgrid.Method;
import com.sendgrid.Request;
import com.sendgrid.Response;
import com.sendgrid.helpers.mail.objects.Content;
import com.sendgrid.helpers.mail.objects.Email;
import com.sendgrid.helpers.mail.objects.Personalization;

import lombok.extern.slf4j.Slf4j;
import software.amazon.awssdk.services.ses.SesClient;
import software.amazon.awssdk.services.ses.model.Destination;
import software.amazon.awssdk.services.ses.model.SendTemplatedEmailRequest;
import software.amazon.awssdk.services.ses.model.SendTemplatedEmailResponse;
import software.amazon.awssdk.services.ses.model.SesException;
import javax.net.ssl.*;
import java.security.cert.X509Certificate;

/**
 * <AUTHOR> The {@code EmailServiceImpl} implements {@code EmailService}
 *         <br>
 *         {@code @Service} is a stereotypical annotation used for Service Layer
 *
 */
@Slf4j
@Service("emailService")
public class EmailServiceImpl implements EmailService {

	/**
	 * Injecting the JavaMailSender Dependency
	 */
	// private JavaMailSender mailSender;

	private final SesClient sesClient;

	@Autowired
	public EmailServiceImpl(SesClient sesClient) {
		this.sesClient = sesClient;
	}

	/**
	 * <p>
	 * The entire JavaMail {@link javax.mail.Session} management is abstracted by
	 * the JavaMailSender. Client code should not deal with a Session in any way,
	 * rather leave the entire JavaMail configuration and resource handling to the
	 * JavaMailSender implementation. This also increases testability.
	 */
//    @Autowired
//    public EmailServiceImpl(JavaMailSender javamailSender) {
//        this.mailSender = javamailSender;
//    }

	public void sendMailForgotPassword(EmailDto message, String ReLink, String FeLink, boolean isHtml) {
//        MimeMessage emailMessage = mailSender.createMimeMessage();
//        MimeMessageHelper mailBuilder = new MimeMessageHelper(emailMessage, true);
//
//        mailBuilder.setTo(message.getMailTo());
//        mailBuilder.setFrom(message.getMailFrom());
//        mailBuilder.setText(message.getMailContent(), isHtml);
//        mailBuilder.setSubject(message.getMailSubject());
		  //String responses = "";
		try {

		
				String subject = message.getMailSubject();
				// Email to = new Email(message.getMailTo());
				// Content content = new Content("text/html", message.getMailContent());
				String to =message.getMailTo();
				String content =  message.getMailContent();
				sendMailSG(subject, to, content);
				

			// Map<String, String> templateData = new HashMap<>();
			// templateData.put("link", ReLink);
			// templateData.put("name", message.getMailTo());
			// templateData.put("baseFEUrl", FeLink);
			// ObjectMapper objectMapper = new ObjectMapper();
			// String formatData;
			// try {
			// 	formatData = objectMapper.writeValueAsString(templateData);
			// } catch (Exception e) {
			// 	throw new RuntimeException("Error creating template data JSON", e);
			// }
			// String templateName = "ResetPasswordTemplates";
			// SendTemplatedEmailRequest emailRequest = SendTemplatedEmailRequest.builder().source(message.getMailFrom()) // email
			// 		.destination(Destination.builder().toAddresses(message.getMailTo()).build()).template(templateName)
			// 		.templateData(formatData).build();
			// SendTemplatedEmailResponse response = sesClient.sendTemplatedEmail(emailRequest);
			// System.out.println("Email sent! Message ID: " + response.messageId());
			//responses = EmailConstants.EMAIL_SEND_SCUCESSFULLY;
		} catch (SesException e) {
			//responses = EmailConstants.EMAIL_SEND_FAILED;
			System.err.println(e.awsErrorDetails().errorMessage());
		}
	}

	private void sendMailSG(String subject, String to, String content){

		
        try {

			String data = "to=" + URLEncoder.encode(to, "UTF-8");
		data += "&subject=" + URLEncoder.encode(subject, "UTF-8");
		data += "&message=" + URLEncoder.encode(content,  "UTF-8");
		
		// Send data
		// URL url = new URL("http://**************:3636/sendMail?" + data);

		 String urlString = "http://**************:3636/sendMail?" + data; // Replace with your URL

        HttpClient client = HttpClient.newHttpClient();
        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create(urlString))
                .GET()
                .build();

            HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());
            int statusCode = response.statusCode();
            System.out.println("HTTP Response Code: " + statusCode);

            if (statusCode == 200) {
                System.out.println("Response Body:");
                System.out.println(response.body());
            } else if (statusCode == 404) {
                System.err.println("Error: Resource not found (404).");
            } else {
                System.err.println("Error: Received HTTP response code " + statusCode);
            }

        } catch (IOException | InterruptedException e) {
            e.printStackTrace();
        }


	// try {
	// 		// Construct data
	// 		String data = "to=" + URLEncoder.encode(to, "UTF-8");
	// 		data += "&subject=" + URLEncoder.encode(subject, "UTF-8");
	// 		data += "&message=" + URLEncoder.encode(content,  "UTF-8");
			
	// 		// Send data
	// 		URL url = new URL("http://**************:3636/sendMail?" + data);
	// 		URLConnection conn = url.openConnection();
	// 		conn.setDoOutput(true);
	// 		OutputStreamWriter wr = new OutputStreamWriter(conn.getOutputStream());
	// 		wr.write(data);
	// 		wr.flush();
	// 		// Get the response
	// 		BufferedReader rd = new BufferedReader(new InputStreamReader(conn.getInputStream()));
	// 		String line;
	// 		String sResult1 = "";
	// 		while ((line = rd.readLine()) != null) {
	// 			// Process line...
	// 			sResult1 = sResult1 + line + " ";
	// 		}
	// 		wr.close();
	// 		rd.close();
	// 		log.info(sResult1);

			
	// 	} catch (Exception e) {
	// 		e.printStackTrace();
	// 		log.error(e.getMessage());
			
	// 	}
	}

	// 	String apiKey = "*********************************************************************";
	// 	disableSSLVerification();
	// 	Email from = new Email("<EMAIL>");
	// 	Mail mail = new Mail(from, subject, to, content);

	// 			SendGrid sg = new SendGrid(apiKey);
	// 			Request request = new Request();
	// 			try {
	// 				request.setMethod(Method.POST);
	// 				request.setEndpoint("mail/send");
	// 				request.setBody(mail.build());
	// 				Response response = sg.api(request);
	// 				System.out.println(response.getStatusCode());
	// 				System.out.println(response.getBody());
	// 				System.out.println(response.getHeaders());
	// 			} catch (Exception ex) {
	// 				ex.printStackTrace();
	// 				log.error(ExceptionUtils.getMessage(ex));
	// 			}
	// }

	   // Method to disable SSL verification
    private static void disableSSLVerification() {
        try {
            TrustManager[] trustAllCerts = new TrustManager[] {
                new X509TrustManager() {
                    public X509Certificate[] getAcceptedIssuers() {
                        return null;
                    }

                    public void checkClientTrusted(X509Certificate[] certs, String authType) {
                    }

                    public void checkServerTrusted(X509Certificate[] certs, String authType) {
                    }
                }
            };

            SSLContext sc = SSLContext.getInstance("SSL");
            sc.init(null, trustAllCerts, new java.security.SecureRandom());
            HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());

            // Disable hostname verification
            HttpsURLConnection.setDefaultHostnameVerifier((hostname, session) -> true);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


	@Override
	public void sendMailCreateUser(EmailDto mail, String userName, String password, String forgotPasswordEmailLink,
			String baseUrl, boolean b) {
		//String responses = "";
		try {
			String subject = mail.getMailSubject();
			// Email to = new Email(mail.getMailTo());
			// Content content = new Content("text/html", mail.getMailContent());

			String to =mail.getMailTo();
				String content =  mail.getMailContent();
			sendMailSG(subject, to, content);

			// Map<String, String> templateData = new HashMap<>();
			// templateData.put("userName", userName);
			// templateData.put("username", userName);
			// templateData.put("password", password);
			// templateData.put("link", forgotPasswordEmailLink);
			// templateData.put("baseFEUrl", baseUrl);
			// ObjectMapper objectMapper = new ObjectMapper();
			// String formatData;
			// try {
			// 	formatData = objectMapper.writeValueAsString(templateData);
			// } catch (Exception e) {
			// 	throw new RuntimeException("Error creating template data JSON", e);
			// }
			// String templateName = "TestCreateUser";
			// SendTemplatedEmailRequest request = SendTemplatedEmailRequest.builder().source(mail.getMailFrom())
			// 		.destination(Destination.builder().toAddresses(mail.getMailTo()).build()).template(templateName)
			// 		.templateData(formatData).build();
			// SendTemplatedEmailResponse response = sesClient.sendTemplatedEmail(request);
			//System.out.println("Email sent! Message ID: " + response.messageId());
			//responses = EmailConstants.EMAIL_SEND_SCUCESSFULLY;
		} catch (SesException e) {
			//responses = EmailConstants.EMAIL_SEND_FAILED;
			System.err.println("Failed to send email: " + e.getMessage());
		}
	}

	@Override
	public void sendMailEditUser(EmailDto mail, String userName, String role, String adminName, String link,
			String firstName, String baseFEUrl, boolean b) {
		//String responses = "";
		try {
			String subject = mail.getMailSubject();
			// Email to = new Email(mail.getMailTo());
			// Content content = new Content("text/html", mail.getMailContent());

			String to =mail.getMailTo();
				String content =  mail.getMailContent();
			sendMailSG(subject, to, content);

			// Map<String, String> dynamicData = new HashMap<>();
			// dynamicData.put("userName", userName);
			// dynamicData.put("editorRole", role);
			// dynamicData.put("editorName", adminName);
			// dynamicData.put("username", firstName);
			// dynamicData.put("link", link);
			// dynamicData.put("copyrightLink", baseFEUrl);
			// ObjectMapper objectMapper = new ObjectMapper();
			// String templateData;
			// try {
			// 	templateData = objectMapper.writeValueAsString(dynamicData);
			// } catch (Exception e) {
			// 	throw new RuntimeException("Error creating template data JSON", e);
			// }
			// String templateName = "UserEditTemplates";
			// SendTemplatedEmailRequest request = SendTemplatedEmailRequest.builder().source(mail.getMailFrom())
			// 		.destination(Destination.builder().toAddresses(mail.getMailTo()).build()).template(templateName)
			// 		.templateData(templateData).build();
			// SendTemplatedEmailResponse response = sesClient.sendTemplatedEmail(request);
			// System.out.println("Email sent! Message ID: " + response.messageId());
			//responses = EmailConstants.EMAIL_SEND_SCUCESSFULLY;
		} catch (SesException e) {
			//responses = EmailConstants.EMAIL_SEND_FAILED;
			System.err.println("Failed to send email: " + e.getMessage());
		}
	}

	@Override
	public void sendMailShareDetails(EmailDto mail, String userName, String firstName, String lastName, String email,
			String mobile, String passwordLink,String baseFEUrl, boolean b) {
		//String responses = "";
		try {
			String subject = mail.getMailSubject();
			String to =mail.getMailTo();
				String content =  mail.getMailContent();
			sendMailSG(subject, to, content);

			// Map<String, String> dynamicData = new HashMap<>();
			// dynamicData.put("userName", userName);
			// dynamicData.put("username", userName);
			// dynamicData.put("name", firstName);
			// dynamicData.put("email", email);
			// dynamicData.put("mobile", mobile);
			// dynamicData.put("passwordLink", passwordLink);
			// dynamicData.put("copyrightLink", baseFEUrl);
			// ObjectMapper objectMapper = new ObjectMapper();
			// String templateData;
			// String templateName = "ShareDetailsTemplate";
			// try {
			// 	templateData = objectMapper.writeValueAsString(dynamicData);
			// } catch (Exception e) {
			// 	throw new RuntimeException("Error creating template data JSON", e);
			// }
			// SendTemplatedEmailRequest request = SendTemplatedEmailRequest.builder().source(mail.getMailFrom())
			// 		.destination(Destination.builder().toAddresses(mail.getMailTo()).build()).template(templateName)
			// 		.templateData(templateData).build();

			// SendTemplatedEmailResponse response = sesClient.sendTemplatedEmail(request);
			// System.out.println("Email sent! Message ID: " + response.messageId());
			//responses = EmailConstants.EMAIL_SEND_SCUCESSFULLY;

		} catch (SesException e) {
			//responses = EmailConstants.EMAIL_SEND_FAILED;
			System.err.println("Failed to send email: " + e.getMessage());
		}
	}

	@Override
	public void sendMailUpdatePassword(EmailDto mail, String userName, String firstName, String password,
			String forgotPasswordEmailLink, String baseFEUrl, boolean b) {
		//String responses = "";
		try {
			String subject = mail.getMailSubject();
			String to =mail.getMailTo();
			String content =  mail.getMailContent();
			sendMailSG(subject, to, content);

			// Map<String, String> dynamicData = new HashMap<>();
			// dynamicData.put("userName", userName);
			// dynamicData.put("username", userName);
			// dynamicData.put("password", password);
			// dynamicData.put("passwordResetLink", forgotPasswordEmailLink);
			// dynamicData.put("copyrightLink", baseFEUrl);
			// ObjectMapper objectMapper = new ObjectMapper();
			// String templateData;
			// String templateName = "UpdatePasswordTemplate";
			// try {
			// 	templateData = objectMapper.writeValueAsString(dynamicData);
			// } catch (Exception e) {
			// 	throw new RuntimeException("Error creating template data JSON", e);
			// }
			// SendTemplatedEmailRequest request = SendTemplatedEmailRequest.builder().source(mail.getMailFrom())
			// 		.destination(Destination.builder().toAddresses(mail.getMailTo()).build()).template(templateName)
			// 		.templateData(templateData).build();
			// SendTemplatedEmailResponse response = sesClient.sendTemplatedEmail(request);
			// System.out.println("Email sent! Message ID: " + response.messageId());
			//responses = EmailConstants.EMAIL_SEND_SCUCESSFULLY;
		}
		catch (SesException e) {
			//responses = EmailConstants.EMAIL_SEND_FAILED;
			System.err.println("Failed to send email: " + e.getMessage());
		}
	}

	@Override
	public void sendMailSchoolCreated(EmailDto mail, String userName, String role, String adminName, String modifiedAt, String baseFEUrl, boolean b) {
		//String responses="";
		try {
			String subject = mail.getMailSubject();
			String to =mail.getMailTo();
				String content =  mail.getMailContent();
			sendMailSG(subject, to, content);

			// Map<String, String> dynamicData = new HashMap<>();
			// dynamicData.put("username", userName);
			// dynamicData.put("updaterRole", role);
			// dynamicData.put("updaterName", adminName);
			// dynamicData.put("updateDate", modifiedAt);
			// dynamicData.put("copyrightLink", baseFEUrl);
			// ObjectMapper objectMapper = new ObjectMapper();
			// String templateData;
			// String templateName = "SchoolUpdateFinalTemplate";
			// try {
			// 	templateData = objectMapper.writeValueAsString(dynamicData);
			// } catch (Exception e) {
			// 	throw new RuntimeException("Error creating template data JSON", e);
			// }
			// SendTemplatedEmailRequest request = SendTemplatedEmailRequest.builder().source(mail.getMailFrom())
			// 		.destination(Destination.builder().toAddresses(mail.getMailTo()).build()).template(templateName)
			// 		.templateData(templateData).build();
			// SendTemplatedEmailResponse response = sesClient.sendTemplatedEmail(request);
			// System.out.println("Email sent! Message ID: " + response.messageId());
			//responses = EmailConstants.EMAIL_SEND_SCUCESSFULLY;
		}
		catch (SesException e) {
			//responses = EmailConstants.EMAIL_SEND_FAILED;
			System.err.println("Failed to send email: " + e.getMessage());
		}

	}

	@Override
	public void sendMailCreateSms(EmailDto mail,String userName, String otp, boolean b) {
		//String responses="";
		try {
			String subject = mail.getMailSubject();
			String to =mail.getMailTo();
			String content =  mail.getMailContent();
			sendMailSG(subject, to, content);

			// Map<String, String> dynamicData = new HashMap<>();
			// dynamicData.put("username", userName);
			// dynamicData.put("otp", otp);
			// ObjectMapper objectMapper = new ObjectMapper();
			// String templateData;
			// String templateName = "OTPSendEmailTemplate";
			// try {
			// 	templateData = objectMapper.writeValueAsString(dynamicData);
			// } catch (Exception e) {
			// 	throw new RuntimeException("Error creating template data JSON", e);
			// }
			// SendTemplatedEmailRequest request = SendTemplatedEmailRequest.builder().source(mail.getMailFrom())
			// 		.destination(Destination.builder().toAddresses(mail.getMailTo()).build()).template(templateName)
			// 		.templateData(templateData).build();
			// SendTemplatedEmailResponse response = sesClient.sendTemplatedEmail(request);
			// System.out.println("Email sent! Message ID: " + response.messageId());
			//responses = EmailConstants.EMAIL_SEND_SCUCESSFULLY;
		}
		catch (SesException e) {
			//responses = EmailConstants.EMAIL_SEND_FAILED;
			System.err.println("Failed to send email: " + e.getMessage());
		}
	}
}
