package com.lms.notification.service.impl;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import javax.validation.Valid;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.aws.messaging.core.QueueMessagingTemplate;
import org.springframework.cloud.aws.messaging.listener.annotation.SqsListener;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.messaging.Message;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.amazonaws.services.sqs.AmazonSQSClient;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lms.notification.component.Translator;
import com.lms.notification.dto.CommonDto;
import com.lms.notification.dto.GetInboxDetails;
import com.lms.notification.dto.MessageDto;
import com.lms.notification.dto.NotificationDto;
import com.lms.notification.dto.NotificationResponse;
import com.lms.notification.entity.Inbox;
import com.lms.notification.entity.MessageTypes;
import com.lms.notification.entity.Messages;
import com.lms.notification.entity.Notification;
import com.lms.notification.entity.NotifyTo;
import com.lms.notification.entity.ProcessedMessages;
import com.lms.notification.enums.ErrorCodes;
import com.lms.notification.exception.NSException;
import com.lms.notification.model.PaginatedResponse;
import com.lms.notification.repository.InboxRepository;
import com.lms.notification.repository.MessagesRepository;
import com.lms.notification.repository.NotificationRepository;
import com.lms.notification.repository.NotificationsRepository;
import com.lms.notification.repository.NotifyToRepository;
import com.lms.notification.repository.ProcessedMessageRepository;
import com.lms.notification.request.dto.NotificationRequestDto;
import com.lms.notification.response.dto.NotificationResponseDto;
import com.lms.notification.service.NotificationService;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class NotificationServiceImpl implements NotificationService {

	Logger logger = LoggerFactory.getLogger(NotificationServiceImpl.class);

	@Autowired
	NotifyToRepository notifyToRepository;

	@Autowired
	NotificationRepository notificationRepository;

	@Autowired
	NotificationsRepository notificationsRepository;

	@Autowired
	private SimpMessagingTemplate simpMessagingTemplate;

	@Autowired
	private QueueMessagingTemplate queueMessagingTemplate;

	@Value("${cloud.aws.end-point.uri}")
	private String endpoint;

	@Autowired
	MessagesRepository messagesrepository;

	@Autowired
	ProcessedMessageRepository processedmessagerepositroy;

	@Autowired
	InboxRepository inboxrepository;

	@Autowired
	AmazonSQSClient sqs;

	@Transactional
	@Override
	public void push(NotificationRequestDto request) {
		try {
		Notification notification = new Notification(request.getTitle(), request.getUrl(), request.getDescription(),
				request.getThumbnail());
		log.info("Before Save");
		notification = notificationRepository.save(notification);
		NotificationResponseDto response = new NotificationResponseDto(null, notification.getTitle(),
				notification.getDescription(), notification.getUrl(), null, false, notification.getCreatedAt());
		log.info("Push notificationrequest :" + request);
		for (String recipientId : request.getRecipientIds()) {
			NotifyTo notifyTo = notifyToRepository.save(new NotifyTo(notification, recipientId, false));
			response.setId(notifyTo.getId());
			log.info("Start Queue");
			simpMessagingTemplate.convertAndSendToUser(recipientId, "/queue/notification", response);
			log.info("End Queue");
 
//			Inbox i = new Inbox();
//			i.setMessage(request.getTitle() + ":" +  request.getDescription());
//			i.setMessageHandlerRoute("enrich");
//			i.setMessageStatus("unread");
//
//			i.setMessagetypeid("QUIZ");
//			LocalDateTime time = LocalDateTime.now();
//			DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
//			String date = time.format(formatter);
//			i.setReadDatetime(date);
//			i.setReceiverId(recipientId);
//			i.setReceiverRole("STUDENT");
//			i.setSenderId(recipientId);
//			i.setSenderRole("TEACHER");
//			i.setSent_Datetime(date);
//
//			log.debug("Inbox detail: " + i);
//			inboxrepository.save(i);

		}
		}
		catch (Exception e) {
			log.error(ExceptionUtils.getMessage(e));
			throw new NSException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("notification.fetch.all.failed", null));
		}
	}

	@Override
	public PaginatedResponse<NotificationResponseDto> getNotificationPaginated(String receiverId, int pageNumber,
			int pageSize) {
		try {
			Long totalElements = 0L;
			Integer totalPages = 0;
			Pageable pageable = PageRequest.of(pageNumber, pageSize, Sort.by(Sort.Direction.ASC, "createdAt"));
			Page<NotificationResponseDto> listResponse = notifyToRepository.findByReceiverId(receiverId, pageable);
			long unreadCount = notifyToRepository.countByReceiverIdAndRead(receiverId, false);
			if (!CollectionUtils.isEmpty(listResponse.getContent())) {
				log.info("Old notifications listed for paginate.");
				totalElements = listResponse.getTotalElements();
				totalPages = listResponse.getTotalPages();
			}
			return new PaginatedResponse<>(totalElements, totalPages, pageSize, (pageNumber + 1), unreadCount,
					listResponse.getContent());
		} catch (Exception e) {
			log.error(ExceptionUtils.getMessage(e));
			throw new NSException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("notification.fetch.all.failed", null));
		}
	}

	@Override
	@Transactional
	public boolean toggleRead(String receiverId, List<String> ids, boolean read) {
		try {
			return notifyToRepository.toggleRead(receiverId, ids, read) > 0;
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new NSException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("notification.toggle.read.failed", null));
		}
	}

	/**
	 * Check the quizzes are notify to the students. If {@code quizType} is empty
	 * then check for Unit/Practice Quiz.
	 * 
	 * @param receiverId
	 * @param quizType
	 * @return
	 */
	@Override
	public boolean checkStudentNotifyQuizRelease(String receiverId, String quizType) {
		try {
			return !StringUtils.isBlank(quizType)
					? notifyToRepository.checkQuizReleaseNotifyToStudent(receiverId, quizType)
					: notifyToRepository.uqOrPQReleaseNotifyToStudent(receiverId);
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new NSException(ErrorCodes.INTERNAL_SERVER_ERROR,
					Translator.toLocale("notification.fetch.all.failed", null));
		}
	}

	/*
	 * @Override
	 * public ResponseNotificationDto sendNotification(MessageDto dto) {
	 * ResponseNotificationDto response=new ResponseNotificationDto();
	 * logger.info("---Start of " +
	 * Thread.currentThread().getStackTrace()[1].getMethodName() + " in "
	 * + Thread.currentThread().getStackTrace()[1].getClassName());
	 * try {
	 * ObjectMapper objectMapper = new ObjectMapper();
	 * String jsonPayload = objectMapper.writeValueAsString(dto);
	 * Message<String> sqsMessage = MessageBuilder.withPayload(jsonPayload)
	 * .build();
	 * queueMessagingTemplate.send(endpoint, sqsMessage);
	 * response.setCode("200");
	 * response.setData(jsonPayload);
	 * } catch (Exception e) {
	 * logger.
	 * error("Error occurred in notification method in NotificationServiceImpl: ",
	 * e);
	 * e.printStackTrace();
	 * }
	 * logger.info("---End of " +
	 * Thread.currentThread().getStackTrace()[1].getMethodName() + " in "
	 * + Thread.currentThread().getStackTrace()[1].getClassName());
	 * return response;
	 * }
	 */

	@Override
	public CommonDto inboxStatusUpdate(String id) {
		CommonDto response = new CommonDto();
		logger.info("---Start of " + Thread.currentThread().getStackTrace()[1].getMethodName() + " in "
				+ Thread.currentThread().getStackTrace()[1].getClassName());
		try {
			String read = "read";
			Inbox inbox = inboxrepository.getById(id);
			logger.info(inbox.toString());
			LocalDateTime date = LocalDateTime.now();
			DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
			String readDateTime = date.format(formatter);
			inboxrepository.updateMessageStatus(id, readDateTime, read);
			response.setCode("200");
			response.setStatus("Inbox table updated sent successfully");
			response.setSenderId(inbox.getSenderId());
			response.setSenderRole(inbox.getSenderRole());
			response.setReceiverId(inbox.getReceiverId());
			response.setReceiverRole(inbox.getReceiverRole());
			response.setSent_Datetime(inbox.getSent_Datetime());
			response.setMessagetypeid(inbox.getMessagetypeid());
			response.setMessage(inbox.getMessage());
			response.setMessageHandlerRoute(inbox.getMessageHandlerRoute());
			response.setMessageStatus(read);
			response.setReadById(inbox.getReadById());
			response.setReadDatetime(readDateTime);
		} catch (Exception e) {
			logger.error("Error occurred in inbox method in NotificationServiceImpl: ", e);
			e.printStackTrace();
		}
		logger.info("---End of " + Thread.currentThread().getStackTrace()[1].getMethodName() + " in "
				+ Thread.currentThread().getStackTrace()[1].getClassName());
		return response;
	}

	@SqsListener("azvasa-notification-queue-test")
	public void loadMessageFromSQS(String message) {
		logger.info("---Start of " + Thread.currentThread().getStackTrace()[1].getMethodName() + " in "
				+ Thread.currentThread().getStackTrace()[1].getClassName());
		logger.info("Message from SQS Queue: {}", message);
		try {
			if (message != null) {
				ObjectMapper objectMapper = new ObjectMapper();
				MessageDto messageData = objectMapper.readValue(message, MessageDto.class);

				MessageTypes mt = new MessageTypes();
				mt.setMessageDescription(messageData.getMessageDescription());
				mt.setMessageName(messageData.getMessageName());
				mt.setMessageTypeId(messageData.getMessageTypeId());
				String json3 = objectMapper.writeValueAsString(messageData.getSupportedChannelsTemplates());
				mt.setSupportedChannelsTemplates(json3);
				notificationsRepository.save(mt);
				Messages m = new Messages();
				String messageJson = objectMapper.writeValueAsString(messageData.getMessageJson());
				m.setMessageJson(messageJson);
				m.setMessageOriginatorId(messageData.getMessageOriginatorId());
				LocalDateTime time = LocalDateTime.now();
				DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
				String formattedStartDate = time.format(formatter);
				m.setMessageReceivedDateTime(formattedStartDate);
				messagesrepository.save(m);

				ProcessedMessages pm = new ProcessedMessages();
				pm.setMessageProcessedDatetime(formattedStartDate);
				String notificationDetails = objectMapper.writeValueAsString(messageData.getNotificationDetails());
				pm.setNotificationDetails(notificationDetails);
				processedmessagerepositroy.save(pm);

				Inbox i = new Inbox();
				i.setMessage(messageData.getNotification().getMessage());
				i.setMessageHandlerRoute(messageData.getNotification().getMessageHandlerRoute());
				i.setMessageStatus(messageData.getNotification().getMessageStatus());
				i.setReadById(messageData.getNotification().getReadById());
				i.setMessagetypeid(messageData.getMessageTypeId());
				i.setReadDatetime(formattedStartDate);
				i.setReceiverId(messageData.getNotification().getReceiverId());
				i.setReceiverRole(messageData.getNotification().getReceiverRole());
				i.setSenderId(messageData.getNotification().getSenderId());
				i.setSenderRole(messageData.getNotification().getSenderRole());
				i.setSent_Datetime(formattedStartDate);
				inboxrepository.save(i);
			} else {
				logger.info("Response not received: {}");
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		logger.info("Details successfully stored in database: {}");
		logger.info("---End of " + Thread.currentThread().getStackTrace()[1].getMethodName() + " in "
				+ Thread.currentThread().getStackTrace()[1].getClassName());
	}

	@Override
	public GetInboxDetails getInboxDetails(String receiverId) {
		GetInboxDetails response = new GetInboxDetails();
		try {
			Long startDate =  new Date().getTime() - 2592000000l;
			Long endDate = new Date().getTime();
//			DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
//			String formattedStartDate = startDate.format(formatter);
//			String formattedEndDate = endDate.format(formatter);
			String messageStatus = "unread";
			List<Inbox> messages = inboxrepository.getLastThirtyDaysMessages(receiverId, startDate, endDate,
					messageStatus);
			List<NotificationDto> content = messages.stream()
					.map(this::mapToDto)
					.collect(Collectors.toList());
			response.setMessages(content);
			response.setCode("200");
			response.setCount(content != null ? content.size() : 0);
			response.setStatus("Inbox table retrieved successfully");
		} catch (Exception e) {
			e.printStackTrace();
		}
		return response;
	}

	private NotificationDto mapToDto(Inbox entity) {
		NotificationDto dto = new NotificationDto();
		try {
			dto.setSenderId(entity.getSenderId());
			dto.setSenderRole(entity.getSenderRole());
			dto.setReceiverId(entity.getReceiverId());
			dto.setReceiverRole(entity.getReceiverRole());
			dto.setMessagetypeid(entity.getMessagetypeid().toString());
			dto.setMessageHandlerRoute(entity.getMessageHandlerRoute());
			dto.setMessageStatus(entity.getMessageStatus());
			dto.setReadById(entity.getReadById());
			dto.setReadDatetime(entity.getReadDatetime());
			dto.setSent_Datetime(entity.getSent_Datetime());
			dto.setMessage(entity.getMessage());
		} catch (Exception e) {
			e.printStackTrace();
		}
		return dto;
	}

	@Override
	public String notification(MessageDto dto) {
		List<MessageDto> dtos = new ArrayList<>();
		dtos.add(dto);

		return notifications(dtos);
	}

	@Override
	public String notifications(List<MessageDto> dtoList) {
		String response = null;
		String methodName = Thread.currentThread().getStackTrace()[1].getMethodName();
		String className = Thread.currentThread().getStackTrace()[1].getClassName();
		logger.info("---Start of " + methodName + " in " + className);
		try {
			for (MessageDto dto : dtoList) {
				ObjectMapper objectMapper = new ObjectMapper();
				String jsonPayload = objectMapper.writeValueAsString(dto);
				Message<String> sqsMessage = MessageBuilder.withPayload(jsonPayload).build();
				queueMessagingTemplate.send(endpoint, sqsMessage);
				response = "Notification sent successfully";
			}
		} catch (Exception e) {
			logger.error("Error occurred in notification method in NotificationServiceImpl: ", e);
			e.printStackTrace();
		}
		logger.info("---End of " + methodName + " in " + className);
		return response;
	}

	@Override
	public NotificationResponse pushNotification(@Valid NotificationRequestDto request) {
		NotificationResponse responseDto=new NotificationResponse();

		try {

		Notification notification = new Notification(request.getTitle(), request.getUrl(), request.getDescription(),
				request.getThumbnail());
		
		notification = notificationRepository.save(notification);
		NotificationResponseDto response = new NotificationResponseDto(null, notification.getTitle(),
				notification.getDescription(), notification.getUrl(), null, false, notification.getCreatedAt());
		log.debug("Push notificationrequest :" + request);
		for (String recipientId : request.getRecipientIds()) {
			NotifyTo notifyTo = notifyToRepository.save(new NotifyTo(notification, recipientId, false));
			response.setId(notifyTo.getId());
			simpMessagingTemplate.convertAndSendToUser(recipientId, "/queue/notification", response);

			Inbox i = new Inbox();
			i.setMessage(request.getTitle() + ":" +  request.getDescription());
			i.setMessageHandlerRoute("enrich");
			i.setMessageStatus("unread");

			i.setMessagetypeid("QUIZ");
			LocalDateTime time = LocalDateTime.now();
			DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
			String date = time.format(formatter);
			i.setReadDatetime(date);
			i.setReceiverId(recipientId);
			i.setReceiverRole("STUDENT");
			i.setSenderId(recipientId);
			i.setSenderRole("TEACHER");
			i.setSent_Datetime(date);

			log.debug("Inbox detail: " + i);
			inboxrepository.save(i);
			responseDto.setMessage("Saved Successfully");

		}

		}
		 catch (Exception e) {
				logger.error("Error occurred in pushNotification method in NotificationServiceImpl: ", e);
				e.printStackTrace();
			}
		logger.info("---End of " + Thread.currentThread().getStackTrace()[1].getMethodName() + " in "
				+ Thread.currentThread().getStackTrace()[1].getClassName());
		return responseDto;

	}


}
