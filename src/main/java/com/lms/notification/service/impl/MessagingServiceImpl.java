package com.lms.notification.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Service;

import com.lms.notification.request.dto.CreateUserRequestDto;
import com.lms.notification.service.MessagingService;
import org.springframework.util.concurrent.ListenableFuture;
import org.springframework.util.concurrent.ListenableFutureCallback;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class MessagingServiceImpl implements MessagingService {

    @Value("${app.kafka.producer.topic}")
    private String emailTopic;

    @Autowired
    private KafkaTemplate<String, CreateUserRequestDto> kafkaCreateMail;

    @Override
    public void publishEmailRequest(CreateUserRequestDto request) {

        log.info(String.format("$$$$ => Producing message CreateUserRequestDto: %s", request));

        ListenableFuture<SendResult<String, CreateUserRequestDto>> future = this.kafkaCreateMail.send(emailTopic, request);

        future.addCallback(new ListenableFutureCallback<>() {
            @Override
            public void onFailure(Throwable ex) {
                log.info("Unable to send message=[ {} ] due to : {}", request, ex.getMessage());
            }

            @Override
            public void onSuccess(SendResult<String, CreateUserRequestDto> result) {
                log.info("Sent message=[ {} ] with offset=[ {} ]", request, result.getRecordMetadata().offset());

            }

        });

    }

}
