package com.lms.notification.service;

import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Service;

import com.lms.notification.entity.SmsAlertBody;
import com.lms.notification.response.dto.SmsSendResponseDTO;

/**
 * <AUTHOR>
 * @since 24-Mar-2022
 *
 */
@Service
@PropertySource("classpath:application-dev.properties")
public interface SMSAlertService {

    public SmsAlertBody sendAlertSms(SmsAlertBody smsAlertBody);

	public String sendSmsUser(SmsSendResponseDTO smsSendResponseDTO);
}
