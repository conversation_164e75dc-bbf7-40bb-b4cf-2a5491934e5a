package com.lms.notification.service;

import javax.mail.MessagingException;

import com.lms.notification.dto.EmailDto;

/**
 * <AUTHOR> The UserService interface
 */
public interface EmailService {

	/**
	 * sendMail details
	 *
	 * @param EmailDto,isHtml
	 * @return
	 */
	public void sendMailForgotPassword(EmailDto message, String ReLink, String FeLink, boolean isHtml);

	public void sendMailCreateUser(EmailDto mail, String userName, String password, String forgotPasswordEmailLink,
			String baseUrl, boolean b);

	public void sendMailEditUser(EmailDto mail, String userName, String role, String link, String adminName, String firstName, String baseFEUrl, boolean b);

	public void sendMailShareDetails(EmailDto mail,  String userName,String firstName, String lastName, String email, String mobile, String passwordLink, String baseFEUrl, boolean b);

	public void sendMailUpdatePassword(EmailDto mail, String userName, String firstName, String password,
			String forgotPasswordEmailLink, String baseFEUrl, boolean b);

	public void sendMailSchoolCreated(EmailDto mail,String userName, String role, String adminName, String modifiedAt, String baseFEUrl,boolean b);

	public void sendMailCreateSms(EmailDto mail, String userName, String otp, boolean b);

}
