package com.lms.notification.service;

import java.util.List;

import javax.validation.Valid;

import com.lms.notification.dto.CommonDto;
import com.lms.notification.dto.GetInboxDetails;
import com.lms.notification.dto.MessageDto;
import com.lms.notification.dto.NotificationResponse;
import com.lms.notification.model.PaginatedResponse;
import com.lms.notification.request.dto.NotificationRequestDto;
import com.lms.notification.response.dto.NotificationResponseDto;

public interface NotificationService {
    
    /**
     * create notification 
     * @param request
     */
    public void push(NotificationRequestDto request);
    
    /**
     * Get list of notifications by receiver id
     * @param receiverId
     * @param pageNumber
     * @param pageSize
     * @return 
     */
    public PaginatedResponse<NotificationResponseDto> getNotificationPaginated(String receiverId, int pageNumber, int pageSize);

    /**
     * Toggle read/unread notification(s)
     * @param receiverId
     * @param ids
     * @param read 
     * @return  
     */
    public boolean toggleRead(String receiverId, List<String> ids, boolean read);

	/**
	 * Check the quizzes are notify to the students. If {@code quizType} is empty
	 * then check for Unit/Practice Quiz.
	 * 
	 * @param receiverId
	 * @param quizType
	 * @return
	 */
	boolean checkStudentNotifyQuizRelease(String receiverId, String quizType);
	
	//ResponseNotificationDto sendNotification(MessageDto dto);
	 
	CommonDto inboxStatusUpdate(String id);

	public GetInboxDetails getInboxDetails(String receiverId);
	
	String notifications(List<MessageDto> dto);
	String notification(MessageDto dto);

	public NotificationResponse pushNotification(@Valid NotificationRequestDto request);
}
