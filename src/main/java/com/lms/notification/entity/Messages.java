package com.lms.notification.entity;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import javax.persistence.Entity;
import javax.persistence.Table;

import org.hibernate.annotations.ColumnTransformer;

import javax.persistence.Column;
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor

@Entity
@Table(name="messages")
public class Messages extends AuditMetadata{
	
	@Column(name = "message_originator_id")
	private String messageOriginatorId;
	
	@Column(name = "message_received_datetime")
	private String messageReceivedDateTime;
	
	@Column(columnDefinition = "jsonb", name = "message_json")
	@ColumnTransformer(write = "?::jsonb")
	private String messageJson;
}
