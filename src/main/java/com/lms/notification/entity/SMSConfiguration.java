package com.lms.notification.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Table;

import com.lms.notification.enums.SmsAction;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
@Entity
@Table(name = "sms_configuration")
public class SMSConfiguration extends AuditMetadata {

	@Enumerated(EnumType.STRING)
	@Column(name = "sms_action", unique = true)
	private SmsAction smsAction;
	
	@Column(name = "sms_action_name", unique = true)
	private String smsActionName;

	@Column(name = "template", columnDefinition = "text", unique = true)
	private String template;
	
	@Column(name = "approved_template_id", unique = true)
	private String approvedTemplateId;

}
