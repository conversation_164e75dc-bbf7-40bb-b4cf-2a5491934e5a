package com.lms.notification.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "inbox")
public class Inbox extends AuditMetadata{	

	@Column(name = "sender_id")
	private String senderId;

	@Column(name = "sender_role")
	private String senderRole;

	@Column(name = "receiver_id")
	private String receiverId;

	@Column(name = "receiver_role")
	private String receiverRole;

	@Column(name = "sent_datetime")
	private String sent_Datetime;

	@Column(name = "message_type_id")
	private String messagetypeid;

	@Column(name = "message")
	private String message;

	@Column(name = "message_handler_route")
	private String messageHandlerRoute;

	@Column(name = "message_status")
	private String messageStatus;

	@Column(name = "read_by_id")
	private String readById;

	@Column(name = "read_datetime")
	private String readDatetime;	
}
