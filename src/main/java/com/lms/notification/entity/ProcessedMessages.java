package com.lms.notification.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import org.hibernate.annotations.ColumnTransformer;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "processed_messages")
public class ProcessedMessages extends AuditMetadata{

	@Column(name = "message_processed_datetime")
	private String messageProcessedDatetime;

	@Column(columnDefinition = "jsonb", name = "notification_details")
	@ColumnTransformer(write = "?::jsonb")
	private String notificationDetails;
}
