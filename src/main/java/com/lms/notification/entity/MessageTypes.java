package com.lms.notification.entity;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import javax.persistence.Entity;
import javax.persistence.Table;

import org.hibernate.annotations.ColumnTransformer;

import javax.persistence.Column;
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor

@Entity
@Table(name="message_types")
public class MessageTypes extends AuditMetadata {
	
	@Column(name = "message_type_id")
	private String messageTypeId;
	
	@Column(name = "message_name")
	private String messageName;
	
	@Column(name = "message_description")
	private String messageDescription;
	
	@Column(columnDefinition = "jsonb", name = "supported_channels_templates")
	@ColumnTransformer(write = "?::jsonb")
	private String supportedChannelsTemplates;
}
