package com.lms.notification.websocket;

import com.lms.notification.authentication.JwtUtil;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.messaging.simp.SimpMessageHeaderAccessor;
import org.springframework.messaging.simp.stomp.StompHeaderAccessor;
import org.springframework.messaging.simp.user.SimpUserRegistry;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.messaging.SessionConnectEvent;
import org.springframework.web.socket.messaging.SessionConnectedEvent;
import org.springframework.web.socket.messaging.SessionDisconnectEvent;

/**
 * Created by <PERSON><PERSON><PERSON>
 */
@Component
public class WebSocketEventListener {

    private static final Logger logger = LoggerFactory.getLogger(WebSocketEventListener.class);

    @Autowired
    private JwtUtil jwtUtil;

    @Autowired
    private SimpUserRegistry simpUserRegistry;

    @EventListener
    public void handleWebSocketConnectListener(SessionConnectEvent event) {
        logger.info("SessionConnectEvent");
        SimpMessageHeaderAccessor headerAccessor = SimpMessageHeaderAccessor.wrap(event.getMessage());
        headerAccessor.getSessionAttributes().put("userId", event.getUser().getName());
        headerAccessor.getSessionAttributes().put("fullName", ((StompPrincipal) event.getUser()).getFullName());
    }

    @EventListener
    public void handleWebSocketConnectedListener(SessionConnectedEvent event) {
        logger.info("SessionConnectedEvent");
    }

    @EventListener
    public void handleWebSocketDisconnectListener(SessionDisconnectEvent event) {
        logger.info("SessionDisconnectEvent");
        StompHeaderAccessor headerAccessor = StompHeaderAccessor.wrap(event.getMessage());
        Map<String, Object> attr = headerAccessor.getSessionAttributes();
//        String userId = ((MyUser) ((UsernamePasswordAuthenticationToken) headerAccessor.getMessageHeaders().get("simpUser")).getPrincipal()).getUsername();
        logger.info(attr.toString());
    }

}
