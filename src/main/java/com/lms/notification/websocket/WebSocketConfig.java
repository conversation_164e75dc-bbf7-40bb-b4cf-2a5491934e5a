package com.lms.notification.websocket;

import com.lms.notification.authentication.AuthServiceImpl;
import com.lms.notification.authentication.JwtUtil;
import com.lms.notification.authentication.MyUser;
import java.security.Principal;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.MessageHeaders;
import org.springframework.messaging.simp.SimpMessageHeaderAccessor;
import org.springframework.messaging.simp.SimpMessageType;
import org.springframework.messaging.simp.config.ChannelRegistration;
import org.springframework.messaging.simp.config.MessageBrokerRegistry;
import org.springframework.messaging.support.ChannelInterceptorAdapter;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.socket.WebSocketHandler;
import org.springframework.web.socket.config.annotation.EnableWebSocketMessageBroker;
import org.springframework.web.socket.config.annotation.StompEndpointRegistry;
import org.springframework.web.socket.config.annotation.WebSocketMessageBrokerConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketTransportRegistration;
import org.springframework.web.socket.server.support.DefaultHandshakeHandler;

@Configuration
@EnableWebSocketMessageBroker
public class WebSocketConfig implements WebSocketMessageBrokerConfigurer {

    @Autowired
    private JwtUtil jwtUtil;
    @Autowired
    private AuthServiceImpl authService;

    @Override
    public void registerStompEndpoints(StompEndpointRegistry registry) {
        registry.addEndpoint("/ws").setHandshakeHandler(new CustomHandshakeHandler()).setAllowedOriginPatterns("*").withSockJS();

    }

    @Override
    public void configureMessageBroker(MessageBrokerRegistry registry) {
        registry.setApplicationDestinationPrefixes("/app");
        registry.enableSimpleBroker("/queue", "/topic");   // Enables a simple in-memory broker
        registry.setUserDestinationPrefix("/user");

    }

    @Override
    public void configureWebSocketTransport(WebSocketTransportRegistration registration) {
        registration.setMessageSizeLimit(15 * 8 * 1024 * 1024); // default : 64 * 1024
//        registration.setSendTimeLimit(20 * 10000); // default : 10 * 10000
//        registration.setSendBufferSizeLimit(3 * 512 * 1024); // default : 512 * 1024

    }

    @Override
    public void configureClientInboundChannel(ChannelRegistration registration) {
        registration.setInterceptors(new ChannelInterceptorAdapter() {

            @Override
            public Message<?> preSend(Message<?> message, MessageChannel channel) {
                SimpMessageHeaderAccessor accessor = SimpMessageHeaderAccessor.wrap(message);
                MessageHeaders headers = message.getHeaders();
                SimpMessageType type = (SimpMessageType) headers.get("simpMessageType");
                if (SimpMessageType.CONNECT.equals(type)) {
                    List<String> tokenList = accessor.getNativeHeader("Authorization");
                    try {
                        String token = tokenList.get(0).substring(7);
                        String userName = jwtUtil.extractUsername(token);
                        if (userName != null && SecurityContextHolder.getContext().getAuthentication() == null) {
                            MyUser userDetails = authService.loadUserByUsernameWS(userName, "Bearer " + token);
                            UsernamePasswordAuthenticationToken usernamePasswordAuthenticationToken = new UsernamePasswordAuthenticationToken(userDetails, null, userDetails.getAuthorities());
                            SecurityContextHolder.getContext().setAuthentication(usernamePasswordAuthenticationToken);
                            accessor.setLeaveMutable(false);
                            accessor.setUser(usernamePasswordAuthenticationToken);
                            StompPrincipal stompPrincipal = (StompPrincipal) headers.get("simpUser");
                            stompPrincipal.setName(userDetails.getUsersDto().getId());
                            stompPrincipal.setFullName(userDetails.getUsersDto().getFullName());
                        }
                    } catch (Exception e) {
                        throw new IllegalArgumentException(e.getMessage());
                    }
                }
                accessor.setLeaveMutable(true);
                return MessageBuilder.createMessage(message.getPayload(), accessor.getMessageHeaders());
            }
        });

    }
}

class CustomHandshakeHandler extends DefaultHandshakeHandler {

    @Override
    protected Principal determineUser(ServerHttpRequest request, WebSocketHandler wsHandler, Map<String, Object> attributes) {
        return new StompPrincipal();
    }
}

class StompPrincipal implements Principal {

    private String name;
    private String fullName;

    @Override
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

}
