package com.lms.notification.repository;

import java.util.List;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

import com.lms.notification.entity.NotifyTo;
import com.lms.notification.response.dto.NotificationResponseDto;

@Repository
public interface NotifyToRepository extends CrudRepository<NotifyTo, String> {

    @Query("SELECT new com.lms.notification.response.dto.NotificationResponseDto(n.id, n.notification.title, n.notification.description, n.notification.url, n.notification.thumbnail, n.read, n.notification.createdAt) "
            + "FROM #{#entityName} n WHERE n.notification.deleted=false AND n.notification.active=true AND n.receiverId=:receiverId")
    Page<NotificationResponseDto> findByReceiverId(String receiverId, Pageable pageable);

    @Modifying
    @Query(value = "UPDATE notify_to SET read=:read "
            + "WHERE receiver_id = :receiverId AND id IN (:ids) ", nativeQuery = true)
    public int toggleRead(String receiverId, List<String> ids, boolean read);

    public long countByReceiverIdAndRead(String receiverId, boolean b);
    
	@Query("SELECT DISTINCT count(nf.id) > 0 FROM #{#entityName} nf "
			+ "WHERE nf.deleted=false AND nf.createdAt IS NOT NULL AND nf.receiverId=:receiverId ")
	boolean existsByFilters(String receiverId);
	
	@Query(value = "SELECT COUNT(n.id) > 0 FROM notification n "
			+ "INNER JOIN notify_to nt ON nt.notification_id=n.id "
			+ "WHERE (n.description LIKE '%Unit Quiz%' OR n.description LIKE '%Practice Quiz%') "
			+ "AND nt.receiver_id=:receiverId", nativeQuery = true)
	boolean uqOrPQReleaseNotifyToStudent(String receiverId);
	
	@Query(value = "SELECT COUNT(n.id) > 0 FROM notification n "
			+ "INNER JOIN notify_to nt ON nt.notification_id=n.id "
			+ "WHERE (n.description LIKE CONCAT('%', cast(:quizType as text), '%')) "
			+ "AND nt.receiver_id=:receiverId", nativeQuery = true)
	boolean checkQuizReleaseNotifyToStudent(String receiverId, String quizType);

}
