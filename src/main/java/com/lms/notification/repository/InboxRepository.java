package com.lms.notification.repository;


import java.util.List;

import javax.transaction.Transactional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.lms.notification.entity.Inbox;



// import jakarta.transaction.Transactional;

@Repository
public interface InboxRepository extends JpaRepository<Inbox, String> {
	@Query(value = "SELECT * From inbox where id= :id ", nativeQuery = true)
	Inbox getIdDetails(@Param("id") String id);
 
	
	@Modifying
	@Transactional
	@Query(value = "update inbox set message_status = (:messageStatus),read_datetime=(:readDateTime) where id = (:id)",nativeQuery = true)
	void updateMessageStatus(@Param("id") String id,@Param("readDateTime") String readDateTime,@Param("messageStatus") String messageStatus);
	
	@Query(value = "SELECT * from inbox where receiver_id=(:receiverId) and sent_datetime BETWEEN (:startDate) AND (:endDate) and message_status = (:messageStatus)",nativeQuery = true)
	List<Inbox> getMessages(@Param("receiverId") String receiverId,@Param("startDate") String startDate,@Param("endDate") String endDate,@Param("messageStatus") String messageStatus);

	@Query(value = "SELECT * from inbox where receiver_id=(:receiverId) and created_at BETWEEN (:startDate) AND (:endDate) and message_status = (:messageStatus)",nativeQuery = true)
	List<Inbox> getLastThirtyDaysMessages(@Param("receiverId") String receiverId,@Param("startDate") Long startDate,@Param("endDate") Long endDate,@Param("messageStatus") String messageStatus);
}

