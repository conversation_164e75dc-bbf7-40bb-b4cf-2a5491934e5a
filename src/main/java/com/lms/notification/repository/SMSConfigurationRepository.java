package com.lms.notification.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import com.lms.notification.Projection.SMSProjection;
import com.lms.notification.entity.SMSConfiguration;
import com.lms.notification.enums.SmsAction;

@Repository
public interface SMSConfigurationRepository extends JpaRepository<SMSConfiguration, String> {

	boolean existsByIdAndDeleted(String id, boolean deleted);

	boolean existsBySmsActionAndDeleted(SmsAction smsAction, boolean deleted);
	
	boolean existsByIdAndSmsActionAndDeleted(String id, SmsAction smsAction, boolean deleted);

	boolean existsByTemplateAndDeleted(String template, boolean deleted);

	boolean existsBySmsActionAndTemplateAndDeleted(SmsAction smsAction, String template, boolean deleted);

	SMSConfiguration getByIdAndDeleted(String id, boolean deleted);

	@Query(value = "SELECT id AS id, sms_action AS smsAction, sms_action_name AS smsActionName, template AS template, active AS active "
			+ "FROM sms_configuration " 
			+ "WHERE deleted=false AND active=true "
			+ "AND ((cast(:id AS text) IS NULL) OR (id=cast(:id AS text))) "
			+ "AND ((cast(:smsAction AS text) IS NULL) OR (sms_action=cast(:smsAction AS text))) ", nativeQuery = true)
	SMSProjection findSMSConfigurationByIdOrSmsAction(String id, String smsAction);
	
	@Query(value = "SELECT id AS id, sms_action AS smsAction, sms_action_name AS smsActionName, template AS template, active AS active, created_at AS createdAt "
			+ "FROM sms_configuration WHERE deleted=false "
			+ "AND ((cast(:id AS text) IS NULL) OR (id=cast(:id AS text))) "
			+ "AND ((cast(:smsAction AS text) IS NULL) OR (sms_action=cast(:smsAction AS text))) "
			+ "AND ((:active IS NULL) OR (cast(active AS text) = cast(:active AS text))) "
			+ "AND ((cast(:search AS text) IS NULL) OR ((LOWER(sms_action) LIKE CONCAT(cast(:search AS text),'%')))) "
			+ "OR ((LOWER(sms_action_name) LIKE CONCAT(cast(:search as text),'%')))", nativeQuery = true)
	Page<SMSProjection> findAllSMSConfigurationByPagination(String id, String smsAction, String search,
			Pageable pageable, Boolean active);
	
	@Query(value = "SELECT template AS template, approved_template_id AS approvedTemplateId "
			+ "FROM sms_configuration " + "WHERE deleted=false AND active=true "
			+ "AND ((cast(:smsAction AS text) IS NULL) OR (sms_action=cast(:smsAction AS text))) ", nativeQuery = true)
	SMSProjection getBySMSAction(String smsAction);

}
