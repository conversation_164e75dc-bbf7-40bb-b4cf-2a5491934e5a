package com.lms.notification.controller;

import javax.mail.MessagingException;
import javax.validation.Valid;

import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.lms.notification.component.EmailBuilder;
import com.lms.notification.constants.EmailConstants;
import com.lms.notification.dto.EmailDto;
import com.lms.notification.enums.ErrorCodes;
import com.lms.notification.exception.NSException;
import com.lms.notification.model.LMSResponse;
import com.lms.notification.request.dto.CreateUserRequestDto;
import com.lms.notification.request.dto.EmailRequestDto;
import com.lms.notification.request.dto.SchoolNotificationResponseDto;
import com.lms.notification.request.dto.ShareUserDetailsRequestDto;
import com.lms.notification.response.dto.EmailResponseDto;
import com.lms.notification.response.dto.SmsSendResponseDTO;
import com.lms.notification.service.EmailService;
import com.lms.notification.service.MessagingService;
import com.lms.notification.util.ResponseHelper;

import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * Processes an {@link EmailController} request.
 *
 * <AUTHOR>
 *
 */
@Slf4j
@RestController
@RequestMapping("/v1/api/notification/emails")
public class EmailController {

	@Autowired
	private EmailService emailService;

	@Value("${mail.from.email.id}")
	private String fromEmail;

	@Value("${mail.subject.resetpassword.template}")
	private String resetTemplate;

	@Value("${mail.subject.email}")
	private String subject;

	@Value("${mail.subject.resetpassword.link}")
	private String resetParams;

	@Value("${mail.subject.email.resetpassword}")
	private String resetPassword;

	@Value("${mail.subject.createUser.template}")
	private String createUserTemplate;

	@Value("${mail.subject.createUser.firstName}")
	private String firstName;

	@Value("${mail.subject.createUser.userName}")
	private String userName;

	@Value("${mail.subject.createUser.password}")
	private String password;
	
	@Value("${mail.subject.createUser.roleName}")
	private String roleName;
	
	@Value("${mail.subject.createUser.roleNameOfAdmin}")
	private String roleNameOfAdmin;

	@Value("${mail.subject.createUser.userCreated}")
	private String userCreated;

	@Value("${mail.subject.shareDetails.lastName}")
	private String lastName;

	@Value("${mail.subject.shareDetails.baseFEUrl}")
	private String baseFEUrl;

	@Value("${mail.subject.shareDetails.email}")
	private String email;

	@Value("${mail.subject.shareDetails.mobile}")
	private String mobile;
	
	@Value("${mail.subject.editUser.adminName}")
	private String adminName;
	
	//template images
	@Value("${mail.subject.template.azvasalogo}")
	private String azvasalogo;
	
	@Value("${mail.subject.template.bglogo}")
	private String bglogo;
	
	@Value("${mail.subject.template.resetlogo}")
	private String resetlogo;
	
	@Value("${mail.template.azlogo}")
	private String azlogo;
	
	@Value("${mail.template.azbglogo}")
	private String azbglogo;
	
	@Value("${mail.template.resetpasslogo}")
	private String openLockIcon;

	@Autowired
	MessagingService kafkaservice;
	
	@Value("${mail.subject.otp.send}")
	private String mailSubjecOotpSend;
	
	@Value("${mail.subject.otp.send.template}")
	private String otpSendTemplate;

	/**
	 * Processes an {@link resetpassword} request.
	 *
	 * @RequestBody params
	 * @return send message success or failure
	 *
	 */
	@SuppressWarnings("unchecked")
	@PostMapping("/forgot-password")
	public LMSResponse<EmailResponseDto> forgotPassword(@Valid @RequestBody EmailRequestDto request) {
		EmailResponseDto response = new EmailResponseDto();
		EmailDto mail = new EmailBuilder()
				.From(fromEmail)
				.To(request.getEmail())
				.Template(resetTemplate)
				.AddContext(subject, resetPassword)
				.AddContext(resetParams, request.getResetLink())
				.AddContext(baseFEUrl, request.getBaseFEUrl())
				.AddContext(azvasalogo, azlogo)
				.AddContext(bglogo, azbglogo)
				.AddContext(resetlogo, openLockIcon)
				.Subject(resetPassword).createMail();
		
		try {
			emailService.sendMailForgotPassword(mail,request.getResetLink(),request.getBaseFEUrl(), true);
			response.setMessage(EmailConstants.EMAIL_SEND_SCUCESSFULLY);
//			
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new NSException(ErrorCodes.INTERNAL_SERVER_ERROR, EmailConstants.EMAIL_SEND_FAILED);
		}
		return ResponseHelper.createResponse(new LMSResponse<EmailResponseDto>(), response,
				EmailConstants.EMAIL_SEND_SCUCESSFULLY, EmailConstants.EMAIL_SEND_FAILED);	
		}

	@SuppressWarnings("unchecked")
	@PostMapping("/createUser")
	public LMSResponse<EmailResponseDto> userCreated(@RequestBody CreateUserRequestDto requestDto) {
		String baseUrl = requestDto.getForgotPasswordEmailLink().substring(0, requestDto.getForgotPasswordEmailLink().indexOf("#"));
		//String baseUrl = requestDto.getForgotPasswordEmailLink();
		EmailResponseDto response = new EmailResponseDto();
		EmailDto mail = new EmailBuilder()
				.From(fromEmail)
				.To(requestDto.getToEmail())
				.Template(createUserTemplate)
				.AddContext(subject, "User Created")
				.AddContext(firstName, requestDto.getFirstName())
				.AddContext(roleName, requestDto.getRoleName())
				.AddContext(userName, requestDto.getUserName())
				.AddContext(password, requestDto.getPassword())
				.AddContext(resetParams, requestDto.getForgotPasswordEmailLink())
				.AddContext(baseFEUrl, baseUrl)
				.AddContext(azvasalogo, azlogo)
				.AddContext(bglogo, azbglogo)
				.Subject(userCreated).createMail();
		try {
			emailService.sendMailCreateUser(mail,requestDto.getUserName(),requestDto.getPassword(),requestDto.getForgotPasswordEmailLink(),baseUrl, true);
			response.setMessage(EmailConstants.EMAIL_SEND_SCUCESSFULLY);
		} catch (Exception e) {
			throw new NSException(ErrorCodes.INTERNAL_SERVER_ERROR, EmailConstants.EMAIL_SEND_FAILED);
		}
		//response.setMessage("");
		return ResponseHelper.createResponse(new LMSResponse<EmailResponseDto>(), response,
				EmailConstants.EMAIL_SEND_SCUCESSFULLY, EmailConstants.EMAIL_SEND_FAILED);	
	}
//	
	@SuppressWarnings("unchecked")
	@PostMapping("/edit-user")
	public LMSResponse<EmailResponseDto> editUser(@RequestBody CreateUserRequestDto requestDto) {
		//String baseUrl = requestDto.getForgotPasswordEmailLink().substring(0, requestDto.getForgotPasswordEmailLink().indexOf("#"));
		EmailResponseDto response = new EmailResponseDto();
		EmailDto mail = new EmailBuilder()
				.From(fromEmail)
				.To(requestDto.getToEmail())
				.Template("user-edit.html")
				.AddContext(subject, "Personal details changed")
				.AddContext(firstName, requestDto.getFirstName())
				.AddContext(roleNameOfAdmin, requestDto.getRoleNameOfAdmin())
				.AddContext(userName, requestDto.getUserName())
				.AddContext(adminName, requestDto.getAdminName())
				.AddContext(resetParams, requestDto.getForgotPasswordEmailLink())
				.AddContext(baseFEUrl, requestDto.getBaseFEUrl())
				.AddContext(azvasalogo, azlogo)
				.AddContext(bglogo, azbglogo)
				.Subject("Personal details changed").createMail();
		try {
			emailService.sendMailEditUser(mail,requestDto.getUserName(),requestDto.getRoleNameOfAdmin(),requestDto.getAdminName(),requestDto.getForgotPasswordEmailLink(),requestDto.getFirstName(),requestDto.getBaseFEUrl(), true);
			response.setMessage(EmailConstants.EMAIL_SEND_SCUCESSFULLY);
		} catch (Exception e) {
			throw new NSException(ErrorCodes.INTERNAL_SERVER_ERROR, EmailConstants.EMAIL_SEND_FAILED);
		}
		//response.setMessage("");
		return ResponseHelper.createResponse(new LMSResponse<EmailResponseDto>(), response,
				EmailConstants.EMAIL_SEND_SCUCESSFULLY, EmailConstants.EMAIL_SEND_FAILED);	}

	@SuppressWarnings("unchecked")
	@PostMapping("/kafakaTest")
	public LMSResponse<EmailResponseDto> kafkaTest(@RequestBody CreateUserRequestDto requestDto) {
		kafkaservice.publishEmailRequest(requestDto);
		EmailResponseDto response = new EmailResponseDto();
		response.setMessage("");
		return ResponseHelper.createResponse(new LMSResponse<EmailResponseDto>(), response,
				EmailConstants.EMAIL_SEND_SCUCESSFULLY, EmailConstants.EMAIL_SEND_FAILED);
	}

	@SuppressWarnings("unchecked")
	@PostMapping("/share-details")
	@ApiOperation(notes = "Share user details", value = "Only for backed", hidden = true)
	public LMSResponse<EmailResponseDto> shareDetailsOfUser(@RequestBody @Valid ShareUserDetailsRequestDto request) {
		EmailResponseDto response = new EmailResponseDto();
		EmailDto mail = new EmailBuilder()
				.From(fromEmail)
				.To(request.getEmail())
				.Template("share-details.html")
				.AddContext(subject, "Account Information")
				.AddContext(userName, request.getUserName())
				.AddContext(firstName, request.getFirstName())
				.AddContext(lastName, request.getLastName())
				.AddContext(email, request.getEmail())
				.AddContext(mobile, request.getMobile())
				.AddContext(resetParams, request.getPasswordResetUrl())
				.AddContext(baseFEUrl, request.getBaseFEUrl())
				.AddContext(azvasalogo, azlogo)
				.AddContext(bglogo, azbglogo)
				.Subject("Account Information").createMail();
		try {
			emailService.sendMailShareDetails(mail,request.getUserName(),request.getFirstName(),request.getLastName(),request.getEmail(),request.getMobile(),request.getPasswordResetUrl(),request.getBaseFEUrl(), true);
			response.setMessage(EmailConstants.EMAIL_SEND_SCUCESSFULLY);
		} catch (Exception e) {
			throw new NSException(ErrorCodes.INTERNAL_SERVER_ERROR, EmailConstants.EMAIL_SEND_FAILED);
		}
		return ResponseHelper.createResponse(new LMSResponse<EmailResponseDto>(), response,
				EmailConstants.EMAIL_SEND_SCUCESSFULLY, EmailConstants.EMAIL_SEND_FAILED);
	}
	
	@SuppressWarnings("unchecked")
	@PostMapping("/update-password")
	@ApiOperation(notes = "Share user details", value = "Only for backed", hidden = true)
	public LMSResponse<EmailResponseDto> updatePassword(@RequestBody @Valid CreateUserRequestDto request) {
		EmailResponseDto response = new EmailResponseDto();
		EmailDto mail = new EmailBuilder()
				.From(fromEmail)
				.To(request.getToEmail())
				.Template("update-password-by-admin.html")
				.AddContext(subject, "Notice: Password Change Successful")
				.AddContext(userName, request.getUserName())
				.AddContext(firstName, request.getFirstName())
				.AddContext(password, request.getPassword())
				.AddContext(resetParams, request.getForgotPasswordEmailLink())
				.AddContext(baseFEUrl, request.getBaseFEUrl())
				.AddContext(azvasalogo, azlogo)
				.AddContext(bglogo, azbglogo)
				.Subject("Notice: Password Change Successful").createMail();
		try {
			emailService.sendMailUpdatePassword(mail,request.getUserName(),request.getFirstName(),request.getPassword(),request.getForgotPasswordEmailLink(),request.getBaseFEUrl(), true);
			response.setMessage(EmailConstants.EMAIL_SEND_SCUCESSFULLY);
		} catch (Exception e) {
			throw new NSException(ErrorCodes.INTERNAL_SERVER_ERROR, EmailConstants.EMAIL_SEND_FAILED);
		}
		return ResponseHelper.createResponse(new LMSResponse<EmailResponseDto>(), response,
				EmailConstants.EMAIL_SEND_SCUCESSFULLY, EmailConstants.EMAIL_SEND_FAILED);	}
	
	@SuppressWarnings("unchecked")
	@PostMapping("/school-created-or-update")
	@ApiOperation(notes = "After school create or edit", value = "Only for backed", hidden = true)
	public LMSResponse<EmailResponseDto> afterSchoolCreatedOrEdited(@RequestBody SchoolNotificationResponseDto request) {
		EmailResponseDto response = new EmailResponseDto();
		String subjectLine = request.getTypeOfOperation().equals("CREATED") ? "School onboarding" : "School information updated"; 
		String templateName = request.getTypeOfOperation().equals("CREATED") ? "school_created.html" : "school_edit.html";
		
		EmailDto mail = new EmailBuilder()
				.From(fromEmail)
				.To(request.getToEmail())
				.Template(templateName)
				.AddContext(subject, subjectLine)
				.AddContext("signaturyName", request.getSignatoryName())
				.AddContext("schoolName", request.getName())
				.AddContext("createdAt", request.getCreatedAt())
				.AddContext("modifiedAt", request.getModifiedAt())
				.AddContext("roleNameOfAdmin", request.getRoleNameOfAdmin())
				.AddContext("adminName", request.getAdminName())				
				.AddContext("baseFEUrl", request.getBaseFEUrl())
				.AddContext(azvasalogo, azlogo)
				.AddContext(bglogo, azbglogo)
				.Subject(subjectLine).createMail();
		try {
			emailService.sendMailSchoolCreated(mail,request.getSignatoryName(),request.getRoleNameOfAdmin(),request.getAdminName(),request.getModifiedAt(),request.getBaseFEUrl(), true);
			response.setMessage(EmailConstants.EMAIL_SEND_SCUCESSFULLY);
		} catch (Exception e) {
			throw new NSException(ErrorCodes.INTERNAL_SERVER_ERROR, EmailConstants.EMAIL_SEND_FAILED);
		}
		return ResponseHelper.createResponse(new LMSResponse<EmailResponseDto>(), response,
				EmailConstants.EMAIL_SEND_SCUCESSFULLY, EmailConstants.EMAIL_SEND_FAILED);
	}
	
	
	@SuppressWarnings("unchecked")
	@PostMapping("/createSMS")
	public LMSResponse<String> sendEmailUser(@Valid @RequestBody SmsSendResponseDTO request) {
		EmailResponseDto response = new EmailResponseDto();
		EmailDto mail = new EmailBuilder()
				.From(fromEmail)
				.To(request.getUserMail())
				.Template(otpSendTemplate)
				.AddContext("userName", request.getUserName())
				.AddContext("otpCode", request.getOtpCode())
				.AddContext(subject, mailSubjecOotpSend)				
				.Subject(mailSubjecOotpSend).createMail();		
		try {
			emailService.sendMailCreateSms(mail,request.getUserName(),request.getOtpCode(), true);
			response.setMessage(EmailConstants.EMAIL_SEND_SCUCESSFULLY);
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
			throw new NSException(ErrorCodes.INTERNAL_SERVER_ERROR, EmailConstants.EMAIL_SEND_FAILED);
		}
		return ResponseHelper.createResponse(new LMSResponse<EmailResponseDto>(), response,
				EmailConstants.EMAIL_SEND_SCUCESSFULLY, EmailConstants.EMAIL_SEND_FAILED);

	}

}
