package com.lms.notification.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.lms.notification.constants.SmsConstants;
import com.lms.notification.entity.SmsAlertBody;
import com.lms.notification.model.LMSResponse;
import com.lms.notification.response.dto.SmsSendResponseDTO;
import com.lms.notification.service.SMSAlertService;
import com.lms.notification.util.ResponseHelper;

/**
 * <AUTHOR> Sheikh
 * @since 24-Mar-2022
 *
 */
@RestController
@RequestMapping("/v1/api/notification")
public class SMSAlertContoller {

	@Autowired
	private SMSAlertService sMSAlertService;

	/**
	 * <b>sendSms</b> - By calling this API , you can send sms
	 *
	 * @param smsAlertBody
	 * @return - generic response
	 */
	@SuppressWarnings("unchecked")
	@PostMapping("/sms")
	public LMSResponse<SmsAlertBody> sendSms(@RequestBody SmsAlertBody smsAlertBody) {
		SmsAlertBody response = sMSAlertService.sendAlertSms(smsAlertBody);
		return ResponseHelper.createResponse(new LMSResponse<String>(), response, SmsConstants.SMS_SEND_SUCCESS,
				SmsConstants.SMS_SEND_FAIL);

	}

	@SuppressWarnings("unchecked")
	@PutMapping("/sms/send")
	public LMSResponse<String> sendSmsUser(@RequestBody SmsSendResponseDTO smsSendResponseDTO) {
		String smsResponse = sMSAlertService.sendSmsUser(smsSendResponseDTO);
		return ResponseHelper.createResponse(new LMSResponse<String>(), smsResponse, SmsConstants.SMS_SEND_SUCCESS,
				SmsConstants.SMS_SEND_FAIL);

	}

}
