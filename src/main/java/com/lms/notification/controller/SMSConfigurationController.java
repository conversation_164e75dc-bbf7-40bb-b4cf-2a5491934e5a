package com.lms.notification.controller;

import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.lms.notification.component.Translator;
import com.lms.notification.model.LMSResponse;
import com.lms.notification.model.PaginatedResponse;
import com.lms.notification.request.dto.SMSRequestDto;
import com.lms.notification.request.dto.SmsGatewayRequestDto;
import com.lms.notification.response.dto.SMSResponseDto;
import com.lms.notification.service.SMSConfigurationService;
import com.lms.notification.util.ResponseHelper;

import io.swagger.annotations.ApiOperation;
import springfox.documentation.annotations.ApiIgnore;

@RestController
@RequestMapping(value = "/v1/api/notification/sms_configurations")
@ApiIgnore
public class SMSConfigurationController {

	@Autowired
	private SMSConfigurationService smsConfigurationService;

	@PostMapping()
	@Lazy
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "Used to create or update the sms configuration", value = "SMS Configuration")
	public LMSResponse<SMSResponseDto> createOrUpdateSMSConfiguration(@Valid @RequestBody SMSRequestDto request) {
		SMSResponseDto response = smsConfigurationService.createOrUpdateSMSConfiguration(request);
		return ResponseHelper.createResponse(new LMSResponse<SMSResponseDto>(), response,
				Translator.toLocale("sms.configuration.create.success", null),
				Translator.toLocale("sms.configuration.create.failed", null));
	}

	@GetMapping("/{id}/active-toggle")
	@SuppressWarnings("unchecked")
	@Lazy
	@ApiOperation(notes = "Used to activate/deactivate SMS Configuration", value = "Toggle SMS Configuration")
	public LMSResponse<Boolean> updateActive(@PathVariable("id") String id,
			@RequestParam(value = "active") boolean active) {
		Boolean response = smsConfigurationService.updateActiveField(id, active);
		return ResponseHelper.createResponseForFlags(new LMSResponse<Boolean>(), response,
				Translator.toLocale("update.active.success", null), Translator.toLocale("update.active.failed", null));
	}

	@DeleteMapping("/{id}")
	@SuppressWarnings("unchecked")
	@Lazy
	@ApiOperation(notes = "Used to mark SMS Configuration as deleted using SMS Configuration ID", value = "Delete SMS Configuration")
	public LMSResponse<Boolean> removeSMSConfiguration(@PathVariable("id") String id) {
		Boolean response = smsConfigurationService.removeSMSConfiguration(id);
		return ResponseHelper.createResponseForFlags(new LMSResponse<Boolean>(), response,
				Translator.toLocale("sms.configuration.delete.success", null),
				Translator.toLocale("sms.configuration.delete.failed", null));
	}

	@GetMapping("/id-smsAction")
	@SuppressWarnings("unchecked")
	@Lazy
	@ApiOperation(notes = "Used to get SMS Configuration by SMS Configuration ID or SMS Action", value = "get by SMS Configuration ID or SMS Action")
	public LMSResponse<SMSResponseDto> getSMSConfigurationByIdOrSmsAction(
			@RequestParam(value = "id", required = false) String id,
			@RequestParam(value = "smsAction", required = false) String smsAction) {
		SMSResponseDto response = smsConfigurationService.getSMSConfigurationByIdOrSmsAction(id, smsAction);
		return ResponseHelper.createResponse(new LMSResponse<SMSResponseDto>(), response,
				Translator.toLocale("sms.configuration.get.by.id.sms.action.success", null),
				Translator.toLocale("sms.configuration.get.by.id.sms.action.failed", null));
	}

	@GetMapping("/page")
	@SuppressWarnings("all")
	@Lazy
	@ApiOperation(notes = "Used to get SMS Configuration List pagination", value = "Pagination for SMS Configuration")
	public LMSResponse<Page<SMSResponseDto>> getAllSMSConfigurationsByPage(
			@RequestParam(value = "pageNumber", required = true, defaultValue = "0") @Min(0) int pageNumber,
			@RequestParam(value = "pageSize", required = true, defaultValue = "10") @Min(1) @Max(50) int pageSize,
			@RequestParam(value = "sortBy", required = false, defaultValue = "createdAt") String sortBy,
			@RequestParam(value = "sortOrder", required = false, defaultValue = "true") boolean sortOrder,
			@RequestParam(value = "search", required = false) String search,
			@RequestParam(value = "id", required = false) String id,
			@RequestParam(value = "smsAction", required = false) String smsAction,
			@RequestParam(value = "active", required = false) Boolean active) {
		PaginatedResponse<SMSResponseDto> response = smsConfigurationService.getAllSMSConfigurationsByPage(pageNumber,
				pageSize, sortBy, sortOrder, search, id, smsAction, active);
		return ResponseHelper.createResponse(new LMSResponse<Page<SMSResponseDto>>(), response,
				Translator.toLocale("sms.configuration.get.all.success", null),
				Translator.toLocale("sms.configuration.get.all.failed", null));
	}
	
	@PutMapping()
	@Lazy
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "Used to Send the SMS to User", value = "Send SMS")
	public LMSResponse<Boolean> sendSMSToUser(@Valid @RequestBody SmsGatewayRequestDto request) {
		Boolean response = smsConfigurationService.sendSMSToUser(request);
		return ResponseHelper.createResponse(new LMSResponse<Boolean>(), response,
				Translator.toLocale("sms.send.user.success", null),
				Translator.toLocale("sms.send.user.failed", null));
	}

}
