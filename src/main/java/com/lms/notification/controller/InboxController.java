package com.lms.notification.controller;

import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.lms.notification.component.Translator;
import com.lms.notification.dto.CommonDto;
import com.lms.notification.dto.GetInboxDetails;
import com.lms.notification.dto.MessageDto;
import com.lms.notification.dto.ResponseNotificationDto;
import com.lms.notification.model.LMSResponse;
import com.lms.notification.model.PaginatedResponse;
import com.lms.notification.request.dto.NotificationRequestDto;
import com.lms.notification.response.dto.NotificationResponseDto;
import com.lms.notification.service.NotificationService;
import com.lms.notification.util.ResponseHelper;

import io.swagger.annotations.ApiOperation;

@RestController
@RequestMapping(value = "/v1/api/notification/inbox")
public class InboxController {

	@Autowired
	private NotificationService notificationService;

	/**
	 * Feign call for creating notification
	 *
	 * @param request
	 */
	

	/**
	 * Feign call for creating notification
	 *
	 * @param receiverId
	 * @param ids
	 * @param read
	 * @return
	 */
	
	
	@PostMapping("/read/{id}")
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "inbox.", value = "inbox")
	public LMSResponse<CommonDto> inboxStatusUpdate(@PathVariable("id") String id) {
		CommonDto response = notificationService.inboxStatusUpdate(id);
		return ResponseHelper.createResponse(new LMSResponse<CommonDto>(), response,
				Translator.toLocale("inboxStatus.update.success", null),
				Translator.toLocale("inboxStatus.update.failed", null));
	}
		
	@GetMapping("/{receiverId}")
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "getInboxDetails.", value = "getInboxDetails")
	public LMSResponse<GetInboxDetails> getInboxDetails(@PathVariable("receiverId") String receiverId ){
		GetInboxDetails response=notificationService.getInboxDetails(receiverId);		
		return ResponseHelper.createResponse(new LMSResponse<GetInboxDetails>(), response,
				Translator.toLocale("inbox.retrieve.success", null),
				Translator.toLocale("inbox.retrieve.failed", null));
	}
	
	
	
}
