package com.lms.notification.controller;

import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.lms.notification.component.Translator;
import com.lms.notification.dto.MessageDto;
import com.lms.notification.dto.NotificationResponse;
import com.lms.notification.model.LMSResponse;
import com.lms.notification.model.PaginatedResponse;
import com.lms.notification.request.dto.NotificationRequestDto;
import com.lms.notification.response.dto.NotificationResponseDto;
import com.lms.notification.service.NotificationService;
import com.lms.notification.util.ResponseHelper;

import io.swagger.annotations.ApiOperation;

@RestController
@RequestMapping(value = "/v1/api/notification")
public class NotificationController {

	@Autowired
	private NotificationService notificationService;

	/**
	 * Feign call for creating notification
	 *
	 * @param request
	 */
	@PostMapping()
	@SuppressWarnings("unchecked")
	@ApiOperation(value = "", hidden = true)
	public void push(@Valid @RequestBody NotificationRequestDto request) {
		notificationService.push(request);
	}
	
	@PostMapping("/push")
	@SuppressWarnings("unchecked")
	@ApiOperation(value = "", hidden = true)
	public LMSResponse<NotificationResponse> pushNotification(@Valid @RequestBody NotificationRequestDto request) {
		NotificationResponse response=notificationService.pushNotification(request);
		return ResponseHelper.createResponse(new LMSResponse<NotificationResponse>(), response,
				Translator.toLocale("sendNotification.success", null),
				Translator.toLocale("sendNotification.failed", null));
	}

	/**
	 * Feign call for creating notification
	 *
	 * @param receiverId
	 * @param ids
	 * @param read
	 * @return
	 */
	@PutMapping("/toggle-read")
	@SuppressWarnings("unchecked")
	public LMSResponse<Boolean> toggleRead(
			@RequestParam(value = "receiverId", required = true, defaultValue = "ff8081818008e999018008f1c4730000") String receiverId,
			@RequestParam(value = "ids", required = true) List<String> ids,
			@RequestParam(value = "read", required = true) boolean read) {
		Boolean response = notificationService.toggleRead(receiverId, ids, read);
		return ResponseHelper.createResponseForFlags(new LMSResponse<Boolean>(), response,
				Translator.toLocale("notification.toggle.read.success", null),
				Translator.toLocale("notification.toggle.read.failed", null));
	}

	@GetMapping("/page")
	@SuppressWarnings("unchecked")
	public LMSResponse<List<NotificationResponseDto>> getStudentMessagePaginated(
			@RequestParam(value = "receiverId", required = true, defaultValue = "ff8081818008e999018008f1c4730000") String receiverId,
			@RequestParam(value = "pageNumber", required = true, defaultValue = "0") @Min(0) int pageNumber,
			@RequestParam(value = "pageSize", required = true, defaultValue = "10") @Min(1) @Max(50) int pageSize) {
		PaginatedResponse<NotificationResponseDto> response = notificationService.getNotificationPaginated(receiverId,
				pageNumber, pageSize);
		return ResponseHelper.createResponse(new LMSResponse<PaginatedResponse<NotificationResponseDto>>(), response,
				Translator.toLocale("notification.fetch.all.success", null),
				Translator.toLocale("notification.fetch.all.failed", null));
	}

	@GetMapping("/check-student-notified/quiz-release")
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "Checking whether the student get notified the quiz release.", value = "Feign call for student service, hiding from FE", hidden = true)
	public LMSResponse<Boolean> checkStudentNotifyQuizRelease(
			@RequestParam(value = "receiverId", required = true) String receiverId,
			@RequestParam(value = "quizType", required = false) String quizType) {
		boolean response = notificationService.checkStudentNotifyQuizRelease(receiverId, quizType);
		return ResponseHelper.createResponse(new LMSResponse<Boolean>(), response,
				Translator.toLocale("notification.fetch.all.success", null),
				Translator.toLocale("notification.fetch.all.failed", null));
	}
	
	/*@PostMapping("/sendNotification")
	@SuppressWarnings("unchecked")
	@ApiOperation(notes = "notification.", value = "notification")
	public LMSResponse<ResponseNotificationDto> sendNotification(@RequestBody MessageDto dto) {
		ResponseNotificationDto response = notificationService.sendNotification(dto);
		return ResponseHelper.createResponse(new LMSResponse<ResponseNotificationDto>(), response,
				Translator.toLocale("sendNotification.success", null),
				Translator.toLocale("sendNotification.failed", null));
	}*/
	
//	@PutMapping("/inbox/read/{id}")
//	@SuppressWarnings("unchecked")
//	@ApiOperation(notes = "inbox.", value = "inbox")
//	public LMSResponse<CommonDto> inboxStatusUpdate(@PathVariable("id") String id) {
//		CommonDto response = notificationService.inboxStatusUpdate(id);
//		return ResponseHelper.createResponse(new LMSResponse<CommonDto>(), response,
//				Translator.toLocale("inboxStatus.update.success", null),
//				Translator.toLocale("inboxStatus.update.failed", null));
//	}
		
//	@GetMapping("/inbox/{receiverId}")
//	@SuppressWarnings("unchecked")
//	@ApiOperation(notes = "getInboxDetails.", value = "getInboxDetails")
//	public LMSResponse<GetInboxDetails> getInboxDetails(@PathVariable("receiverId") String receiverId ){
//		GetInboxDetails response=notificationService.getInboxDetails(receiverId);
//		return ResponseHelper.createResponse(new LMSResponse<GetInboxDetails>(), response,
//				Translator.toLocale("inbox.retrieve.success", null),
//				Translator.toLocale("inbox.retrieve.failed", null));
//	}
	
	
	@SuppressWarnings("unchecked")
	@PostMapping("/sendNotifications")
	@ApiOperation(notes = "sendNotifications", value = "sendNotifications")
	public LMSResponse<String> notifications(@RequestBody List<MessageDto> dtos) {
		String response=notificationService.notifications(dtos);
		return ResponseHelper.createResponse(new LMSResponse<String>(),response,
				Translator.toLocale("sendNotification.success", null),
				Translator.toLocale("sendNotification.failed", null));
	}

	@SuppressWarnings("unchecked")
	@PostMapping("/sendNotification")
	@ApiOperation(notes = "sendNotification", value = "sendNotification")
	public LMSResponse<String> notification(@RequestBody MessageDto dto) {
		String response=notificationService.notification(dto);
		return ResponseHelper.createResponse(new LMSResponse<String>(),response,
				Translator.toLocale("sendNotification.success", null),
				Translator.toLocale("sendNotification.failed", null));
	}
}
