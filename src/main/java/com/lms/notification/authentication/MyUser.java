package com.lms.notification.authentication;

import com.lms.notification.feign.users.UsersFeignDto;
import java.util.List;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.User;

/**
 *
 * <AUTHOR> <PERSON>
 */
public class MyUser extends User {

    private final UsersFeignDto usersDto;

    public MyUser(UsersFeignDto usersDto, List<SimpleGrantedAuthority> authorities) {
        super(usersDto.getUserName(), usersDto.getPassword(), authorities);
        this.usersDto = usersDto;
    }

    public UsersFeignDto getUsersDto() {
        return usersDto;
    }

}
