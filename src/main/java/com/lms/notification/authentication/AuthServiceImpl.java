package com.lms.notification.authentication;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import com.lms.notification.component.Translator;
import com.lms.notification.enums.ErrorCodes;
import com.lms.notification.exception.NSException;
import com.lms.notification.feign.users.UsersFeignClient;
import com.lms.notification.feign.users.UsersFeignDto;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> The {@code AuthServiceImpl} implements
 * {@code UserDetailsService} <br> {@code @Service} is a stereotypical
 * annotation used for Service Layer
 * <br> {@code Slf4j} is a Logger annotation obtained from Lombok dependency for
 * logging the requests and responses
 */
@Slf4j
@Service("authService")
@RequiredArgsConstructor
public class AuthServiceImpl implements UserDetailsService {

    private final UsersFeignClient usersClient;

    @Override
    public UserDetails loadUserByUsername(String userName) throws UsernameNotFoundException {
        UsersFeignDto usersDto = getUsersDetails(userName);
        List<SimpleGrantedAuthority> authorities = new ArrayList<>();
        return new MyUser(usersDto, authorities);
    }

    public MyUser loadUserByUsernameWS(String userName, String token) throws UsernameNotFoundException {
        UsersFeignDto usersDto = getUsersDetailsWS(userName, token);
        List<SimpleGrantedAuthority> authorities = new ArrayList<>();
        return new MyUser(usersDto, authorities);
    }

    /**
     * Users details get from the users-service by the feign client call.
     *
     * @param userName
     * @return
     */
    private UsersFeignDto getUsersDetails(String userName) {

        if (StringUtils.isEmpty(userName)) {
            throw new NSException(ErrorCodes.BAD_REQUEST, Translator.toLocale("user.name.mandatory", null));
        }
        try {
            // feign call
            return usersClient.getUsersByUsernameForFeign(userName).getData();
        } catch (Exception e) {
            log.error(ExceptionUtils.getStackTrace(e));
            throw new NSException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("user.not.found", null));
        }
    }

    /**
     * Users details get from the users-service by the feign client call.
     *
     * @param userName
     * @param token
     * @return
     */
    private UsersFeignDto getUsersDetailsWS(String userName, String token) {

        if (StringUtils.isEmpty(userName) && StringUtils.isEmpty(token)) {
            throw new NSException(ErrorCodes.BAD_REQUEST, Translator.toLocale("user.name.mandatory", null));
        }
        try {
            // feign call
            return usersClient.getUsersByUsernameForFeign(userName, token).getData();
        } catch (Exception e) {
            log.error(ExceptionUtils.getStackTrace(e));
            throw new NSException(ErrorCodes.INTERNAL_SERVER_ERROR, Translator.toLocale("user.not.found", null));
        }
    }
}
