package com.lms.notification.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * To create a common response model for all the API response. To-Do : once
 * completed use this model for all other response
 *
 * <AUTHOR> <PERSON>
 * @since 0.0.1
 *
 * @param <T>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LMSResponse<T> {

    /**
     * Generic data which will adopt to the different resources
     */
    private T data;

    /**
     * The success/error message of the API requested.
     */
    private String message;

    /**
     * The parameter which indicates the status of API response.
     */
    private boolean success;

    /**
     * The application specific error codes.
     */
    private String errorCode;

    /**
     * The hateoas resource link path of the API will display To-Do: add
     * dependency to pom.xml
     */
    private String path;
}
