package com.lms.notification.request.dto.serializer;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lms.notification.request.dto.NotificationRequestDto;
import java.util.Map;
import org.apache.kafka.common.serialization.Deserializer;

/**
 *
 * <AUTHOR>
 */
public class NotificationRequestDtoDeserializer implements Deserializer<NotificationRequestDto> {

    @Override
    public void configure(Map<String, ?> map, boolean b) {

    }

    @Override
    public NotificationRequestDto deserialize(String arg0, byte[] arg1) {
        ObjectMapper mapper = new ObjectMapper();
        NotificationRequestDto messageRequestDto = null;
        try {
            messageRequestDto = mapper.readValue(arg1, NotificationRequestDto.class);
        } catch (Exception e) {

            e.printStackTrace();
        }
        return messageRequestDto;
    }

    @Override
    public void close() {

    }
}
