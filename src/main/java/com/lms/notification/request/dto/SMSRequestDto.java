package com.lms.notification.request.dto;

import javax.validation.constraints.Pattern;

import com.lms.notification.util.Constants;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class SMSRequestDto {

	@ApiModelProperty(value = "id of the sms action", example = "2c91808483cf75800183cfb4e5500000", position = 1)
	private String id;

	@ApiModelProperty(value = "sms action", example = "USER_CREATION", required = true, position = 2)
	private String smsAction;

	@ApiModelProperty(value = "template of the SMS Action", required = true, position = 3)
	private String template;
	
	@ApiModelProperty(value = "Trai approved template id ", example = "2c91808483cf75800183cfb4e5500000", position = 4)
	@Pattern(regexp = Constants.NUMBER_REGEX, message = Constants.NUMBER_ONLY)
	private String approvedTemplateId;

}
