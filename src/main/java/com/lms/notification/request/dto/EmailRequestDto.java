package com.lms.notification.request.dto;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

import com.lms.notification.util.Constants;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@Data
@NoArgsConstructor
public class EmailRequestDto {

    @NotBlank(message = Constants.MANDATORY_FIELD)
    private String resetLink;

    @NotBlank(message = Constants.MANDATORY_FIELD)
    @Pattern(regexp = Constants.BASIC_EMAIL_REGEX, message = Constants.GIVE_VALID_VALUE)
    private String email;
    
    @ApiModelProperty(value = "FE-Base URL", example = "http://dev.lms.giglabz.in/")
	private String baseFEUrl;
}
