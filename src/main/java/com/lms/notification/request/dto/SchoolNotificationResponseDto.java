package com.lms.notification.request.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * To send the email while create or edit the school
 * 
 * <AUTHOR>
 *
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SchoolNotificationResponseDto {

	private String toEmail;
	private String signatoryName;
	private String name;
	private String roleNameOfAdmin;
	private String adminName;
	private String createdAt;
	private String modifiedAt;
	private String baseFEUrl;
	private String typeOfOperation;
}
