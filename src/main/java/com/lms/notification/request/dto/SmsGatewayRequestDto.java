package com.lms.notification.request.dto;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SmsGatewayRequestDto {

	@ApiModelProperty(value = "firstName of the selected persona", example = "Huma", position = 1)
	private String firstName;

	@ApiModelProperty(value = "userName of the selected persona", example = "Huma.Querasi.MMARS_001", position = 2)
	private String userName;

	@ApiModelProperty(value = "Mobile number of student", example = "8547890756", position = 3)
	private String phoneNumber;

	@ApiModelProperty(value = "Email address of user", example = "<EMAIL>", position = 4)
	private String emailId;

	@ApiModelProperty(value = "last updated", example = "dd-mm-yy hh:mm AM", position = 5)
	private String lastModifiedAt;

	@ApiModelProperty(value = "Name of the chapter", example = "Sentense", position = 6)
	private String chapter;

	@ApiModelProperty(value = "Name of the Quiz", example = "Unit Quiz-1", position = 7)
	private String quizName;

	@ApiModelProperty(value = "total quiz marks", example = "100", position = 8)
	private Integer totalMarks;

	@ApiModelProperty(value = "Date format must be in dd/MM/yyyy format", example = "07/08/2022", position = 9)
	private String startDate;

	@ApiModelProperty(value = "Value has to be in h:mm a", example = "9:30 PM", position = 10)
	private String startTime;

	@ApiModelProperty(value = "total obtained marks", example = "80", position = 11)
	private Integer totalObtainedMark;

	@ApiModelProperty(value = "sms action", example = "USER_CREATION", position = 12)
	private String smsAction;

	@ApiModelProperty(value = "batch operation", example = "true", position = 13)
	private boolean batchOperation = false;

	@ApiModelProperty(value = "batch receptinist", position = 14)
	private List<BatchReceptionistRequestDto> recipientList;

}
