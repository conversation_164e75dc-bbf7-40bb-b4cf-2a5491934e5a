package com.lms.notification.request.dto.serializer;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lms.notification.request.dto.NotificationRequestDto;
import java.util.Map;
import org.apache.kafka.common.serialization.Serializer;

/**
 *
 * <AUTHOR> <PERSON>
 */
public class NotificationRequestDtoSerializer implements Serializer<NotificationRequestDto> {

    @Override
    public void configure(Map<String, ?> map, boolean b) {

    }

    @Override
    public byte[] serialize(String arg0, NotificationRequestDto arg1) {
        byte[] retVal = null;
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            retVal = objectMapper.writeValueAsString(arg1).getBytes();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return retVal;
    }

    @Override
    public void close() {

    }
}
