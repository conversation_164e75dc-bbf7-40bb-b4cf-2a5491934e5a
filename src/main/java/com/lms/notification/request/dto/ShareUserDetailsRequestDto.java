package com.lms.notification.request.dto;

import javax.validation.constraints.NotEmpty;

import com.lms.notification.util.Constants;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@Data
@NoArgsConstructor
public class ShareUserDetailsRequestDto {
	
	@NotEmpty(message = Constants.MANDATORY_FIELD)
	@ApiModelProperty(value = "userName of the selected persona", example = "Huma.Querasi.MMARS_001", position = 1, required = true)
	private String userName;

	@NotEmpty(message = Constants.MANDATORY_FIELD)
	@ApiModelProperty(value = "name of the selected persona", example = "Huma Querasi", position = 2, required = true)
	private String firstName;
	
	@ApiModelProperty(value = "name of the selected persona", example = "Huma Querasi", position = 3, required = true)
	private String lastName;

	@NotEmpty(message = Constants.MANDATORY_FIELD)
	@ApiModelProperty(value = "email for to-address and share in content", example = "<EMAIL>", position = 4, required = true)
	private String email;

	@NotEmpty(message = Constants.MANDATORY_FIELD)
	@ApiModelProperty(value = "mobile of the selected persona", example = "7025890759", position = 5, required = true)
	private String mobile;

	@NotEmpty(message = Constants.MANDATORY_FIELD)
	@ApiModelProperty(value = "FE-Base URL", example = "http://dev.lms.giglabz.in/", position = 6, required = true)
	private String baseFEUrl;

	@NotEmpty(message = Constants.MANDATORY_FIELD)
	@ApiModelProperty(value = "Change password URL", example = "http://dev.lms.giglabz.in/#/setPass?userId=ZD9UNP6jt4bB8b9D/cIrPQxRcumVJ/KijOhwGHz8Z2XZKT2dmXnbSEAxPa55GJBl", position = 7, required = true)
	private String passwordResetUrl;
}
