package com.lms.notification.request.dto;

import com.lms.notification.util.Constants;
import java.util.List;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import lombok.Data;

@Data
public class NotificationRequestDto {

    @NotBlank(message = Constants.MANDATORY_FIELD)
    private String title;

    private String description;
    
    @NotBlank(message = Constants.MANDATORY_FIELD)
    private String url;
    
    private String thumbnail;
    
    @NotEmpty(message = Constants.MANDATORY_FIELD)
    private List<String> recipientIds;

}
