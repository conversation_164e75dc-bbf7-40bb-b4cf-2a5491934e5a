package com.lms.notification.component;

import javax.mail.MessagingException;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.annotation.KafkaListener;

import com.lms.notification.constants.EmailConstants;
import com.lms.notification.dto.EmailDto;
import com.lms.notification.enums.ErrorCodes;
import com.lms.notification.exception.NSException;
import com.lms.notification.request.dto.CreateUserRequestDto;
import com.lms.notification.service.EmailService;

import lombok.extern.slf4j.Slf4j;

//@Component
@Slf4j
public class KafkaBatchConsumer {

    @Autowired
    private EmailService emailService;

    @Value("${mail.from.email.id}")
    private String fromEmail;

    @Value("${mail.subject.resetpassword.template}")
    private String resetTemplate;

    @Value("${mail.subject.email}")
    private String subject;

    @Value("${mail.subject.resetpassword.link}")
    private String resetParams;

    @Value("${mail.subject.email.resetpassword}")
    private String resetPassword;

    @Value("${mail.subject.createUser.template}")
    private String createUserTemplate;

    @Value("${mail.subject.createUser.firstName}")
    private String firstName;

    @Value("${mail.subject.createUser.userName}")
    private String userName;

    @Value("${mail.subject.createUser.userCreated}")
    private String userCreated;

    @KafkaListener(topicPattern = "${app.kafka.producer.topic}")
    public void listenToProjectStatusChange(CreateUserRequestDto payload) {
        log.info("Request for project status change received: " + payload.toString());

        EmailDto mail = new EmailBuilder()
                .From(fromEmail)
                .To(payload.getToEmail())
                .Template(createUserTemplate)
                .AddContext(subject, "User Created")
                .AddContext(firstName, payload.getFirstName())
                .AddContext(userName, payload.getUserName())
                .AddContext(resetParams, payload.getForgotPasswordEmailLink())
                .Subject(userCreated).createMail();

        try {
            //emailService.sendMail(mail, true);
        } catch (Exception e) {
            throw new NSException(ErrorCodes.INTERNAL_SERVER_ERROR, EmailConstants.EMAIL_SEND_FAILED);
        }
    }

}
