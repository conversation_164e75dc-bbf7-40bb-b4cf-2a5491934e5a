#Authentication or Authorization
username.mandatory = userName is mandatory.
token.mandatory = Token is mandatory.
user.access=User access denied
user.invalid.username=Invalid user name or password
user.token = Token not valid
user.authenticated.scucessfully = User authenticated successfully
user.authentication.failed = User authentication failed
token.validate.scucessfully = Token validate successfully
token.validate.failed = Token validation failed
malformed.Jwt = Malformed JWT token.
access.denied = User access denied.

#validation messages
mandatory.field = , mandatory field.

#misc
internal.server.error = Something went wrong.
invalid.data = Invalid data, please check the request.
field.not.exist = Field not exist.

#feign call USERS-SERVICE
user.name.mandatory = userName not found.
user.not.found = User details not found.

#notification
notification.fetch.all.success=Notifications fetched successfully.
notification.fetch.all.failed=Failed to fetch notifications.

notification.toggle.read.success=Read toggle was successful.
notification.toggle.read.failed=Failed to toggle read.

#SMS Configuration
sms.configuration.create.success=Successfully created the SMS Configuration.
sms.configuration.create.failed=Failed to create the SMS Configuration.
sms.configuration.not.found=SMS Configuration not found.
sms.action.exist=SMS action already exist, please check the request.
sms.template.exist=SMS template already exist, please check the request.
sms.action.invalid.data=Please pass the valid smsAction.
update.active.success = Active status updated successfully.
update.active.failed = failed to update active status.
sms.configuration.delete.success = SMS Configuration deleted successfully.
sms.configuration.delete.failed = failed to delete SMS Configuration.
sms.configuration.request.parameter=Please pass the required parameter.
sms.configuration.get.by.id.sms.action.failed=Failed to fetch the SMS Configuration by id or SMS Action.
sms.configuration.get.by.id.sms.action.success=Successfully to fetched the SMS Configuration by id or SMS Action.
sms.configuration.correct.parameter=Please check the id and SMS action.
sms.configuration.get.all.success = SMS Configuration list fetched successfully.
sms.configuration.get.all.failed = Failed to fetch the SMS Configuration list.
sms.send.user.success=Successfully sent the SMS to the user.
sms.send.user.failed=Failed to sent the SMS to the user.

#Inbox
sendNotification.success=Send notification success.
sendNotification.failed=Send notification failed.

inboxStatus.update.success=inbox status update success.
inboxStatus.update.failed=inbox status update failed.

inbox.retrieve.success=inbox retrieve success.
inbox.retrieve.failed=inbox retrieve failed.
