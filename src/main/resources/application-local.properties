spring.datasource.url=*********************************************************
spring.datasource.username= postgres
spring.datasource.password= postgres

spring.jpa.properties.hibernate.jdbc.lob.non_contextual_creation= true
spring.jpa.properties.hibernate.dialect = com.lms.notification.config.PostgreSQL10JsonDialect
spring.jpa.hibernate.ddl-auto = update

spring.datasource.hikari.minimumIdle: 10
spring.datasource.hikari.maximumPoolSize: 20
spring.datasource.hikari.idleTimeout: 120000
spring.datasource.hikari.connectionTimeout: 300000
spring.datasource.hikari.leakDetectionThreshold: 300000

server.port=9002
spring.application.name:NOTIFICATION-SERVICE

#jwt
jwt.secret=ultra-secure-and-ultra-long-secret-lms
jwt.expirationDateInMs=8600000
jwt.refreshExpirationDateInMs=8600000

#Twilio SMS Properties test
twilio.account-sid=**********************************
twilio.auth-token=ca285208da1b3a6f51c5f1705e493891
twilio.phone-number=+***********


spring.kafka.bootstrap-servers= = localhost:9092
spring.kafka.producer.key-serializer = org.apache.kafka.common.serialization.StringSerializer
spring.kafka.producer.value-serializer = org.springframework.kafka.support.serializer.JsonSerializer
app.kafka.producer.topic=email-topic
app.kafka.producer.partition=1
app.kafka.producer.replicas=1


#=============== consumer  =======================
spring.kafka.consumer.client-id=${spring.application.name}
# Specify the default consumer group ID -- > because in kafka, the consumers in the same group will not read the same message, relying on the group ID to set the group name
spring.kafka.consumer.group-id=email
# Smallest and largest are only valid. If smallest starts to read again from 0, then largest will read from the offset of logfile. In general, we set up smalles
spring.kafka.consumer.auto-offset-reset=earliest
spring.kafka.consumer.enable-auto-commit=false
# If 'enable.auto.commit' is true, the consumer offset is automatically submitted to Kafka in milliseconds, with a default value of 5000.
spring.kafka.consumer.auto-commit-interval=100
spring.kafka.consumer.max-poll-records=10
# Specifies the encoding and decoding method of message key and message body
spring.kafka.consumer.key-deserializer=org.springframework.kafka.support.serializer.ErrorHandlingDeserializer
spring.kafka.consumer.value-deserializer=org.springframework.kafka.support.serializer.ErrorHandlingDeserializer

spring.json.trusted.packages='*'
spring.deserializer.key.delegate.class=org.apache.kafka.common.serialization.StringDeserializer
spring.deserializer.value.delegate.class=org.springframework.kafka.support.serializer.JsonDeserializer

#Email template logo
mail.template.azlogo=https://azvasa-lms-dev-s3-app-backup.s3.ap-south-1.amazonaws.com/User-Service/Common-Content/bb602fb6-0a19-4e09-89bf-a53fe7257fe6_logo.png
mail.template.azbglogo=https://azvasa-lms-dev-s3-app-backup.s3.ap-south-1.amazonaws.com/User-Service/Common-Content/d10ec595-5c00-4f12-8988-1905dfc2caa3_bg-logo.png
mail.template.resetpasslogo=https://azvasa-lms-dev-s3-app-backup.s3.ap-south-1.amazonaws.com/User-Service/Common-Content/65a27ced-c399-4cc7-b4c7-d58117e43752_reset-password.png

#sms-gateway config
sms.gateway.url = http://api.bulksmsgateway.in/sendmessage.php?
sms.gateway.username = Azvasa
sms.gateway.password = Azvasa@123
sms.gateway.sender = AZVASA
sms.gateway.type = 3

cloud.aws.region.static= ap-south-1
cloud.aws.region.auto= false
# cloud.aws.credentials.access-key= ********************
# cloud.aws.credentials.secret-key= NlEOEl1bqWbXhMhNSIJX6VwSOlP4OewPl9suERjY
# cloud.aws.end-point.uri= https://sqs.ap-south-1.amazonaws.com/842658673384/azvasa-notification-queue-test


api.user.service.url=http://localhost:9001
api.notification.url=http://localhost:9002
api.fileupload.url=http://localhost:9003
api.master.service.url=http://localhost:9004
api.content.service.url=http://localhost:9005
api.teacher.service.url=http://localhost:9006
api.student.service.url=http://localhost:9007
