host.full.dns.auth.link=http://oauthserver.example.com:8088
app.client.id=test-client
app.client.secret=clientSecret
auth.server.schem=http
spring.mvc.pathmatch.matching-strategy = ANT_PATH_MATCHER


app.swagger.module=LMS-Module
app.swagger.termofurl=https://www.lms.com
app.swagger.contact.name=Learning Management System
app.swagger.contact.url=https://www.lms.com
app.swagger.contact.email=<EMAIL>
app.swagger.license.type=Open-Source
app.swagger.license.url=https://www.lms.com
app.swagger.version=2.2.0


