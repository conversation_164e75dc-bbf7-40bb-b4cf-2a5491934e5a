spring.mail.host=smtp.office365.com
spring.mail.port=587
spring.mail.username=<PERSON><PERSON><PERSON><PERSON>@azvasa.in
spring.mail.password=D)949637478191us
#spring.mail.username=<EMAIL>
#spring.mail.password=Giglabz@2020
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true
spring.mail.properties.mail.smtp.connecttiontimeout=25000
spring.mail.properties.mail.smtp.timeout=25000
spring.mail.properties.mail.smtp.writetimeout=25000
spring.mail.properties.mail.smtp.ssl.trust=*
spring.mail.properties.mail.debug=true
spring.mail.properties.mail.smtp.ssl.enable=false

# JVM properties for trustStore location
javax.net.ssl.trustStore=classpath:azvasa.online.jks
javax.net.ssl.trustStorePassword=azvasa123

mail.subject.email=subject
mail.from.email.id=<EMAIL>
#mail.from.email.id=<EMAIL>

#Reset password
mail.subject.email.resetpassword=Reset your Azvasa password
mail.subject.resetpassword.link=link
mail.subject.resetpassword.template=resetpassword.html

#create user
mail.subject.createUser.template=user-created.html
mail.subject.createUser.firstName = firstName
mail.subject.createUser.userName = userName
mail.subject.createUser.password = password
mail.subject.createUser.userCreated = User Created
mail.subject.createUser.roleName = roleName
mail.subject.createUser.roleNameOfAdmin = roleNameOfAdmin

#create user
mail.subject.editUser.adminName = adminName

#share user details
mail.subject.shareDetails.template=share-details.html
mail.subject.shareDetails.email=email
mail.subject.shareDetails.lastName=lastName
mail.subject.shareDetails.baseFEUrl=baseFEUrl
mail.subject.shareDetails.mobile=mobile

#Giglabz Server or Local
mail.subject.template.azvasalogo=azvasalogo
mail.subject.template.bglogo=bglogo
mail.subject.template.resetlogo=resetlogo

mail.subject.otp.send=Action Needed: Use This OTP to Reset Your Password
mail.subject.otp.send.template=mailOtp.html

# aws.accessKey=********************
# aws.secretKey=HmHI1y+olAlH/oq6+IWtFsMFIL9IxFPDX0199k42
# aws.region=ap-south-1




