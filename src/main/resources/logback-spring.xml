<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- Include Default Spring Boot Configurations -->
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
    <include resource="org/springframework/boot/logging/logback/console-appender.xml"/>

    <!-- Rolling File Appender to Capture Only ERROR Logs -->
    <appender name="FILE-ROLLING" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>app.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
	<fileNamePattern>logs/archived/app.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>5MB</maxFileSize>  <!-- Rotate files at 5MB -->
            <totalSizeCap>100MB</totalSizeCap>  <!-- Cap total storage to 500MB -->
            <maxHistory>7</maxHistory>  <!-- Keep logs for 7 days -->
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %-5p %c{1.} [%t] %m%n</pattern>  <!-- Log pattern -->
        </encoder>
    </appender>

    <!-- Root Logger Set to ERROR Only -->
    <root level="ERROR">
        <appender-ref ref="CONSOLE"/>  <!-- Console logs -->
        <appender-ref ref="FILE-ROLLING"/>  <!-- Rolling file logs -->
    </root>

    <!-- Use Wildcards to Set Specific Frameworks to ERROR -->
    <logger name="org.springframework.*" level="ERROR" additivity="false"/>
    <logger name="org.hibernate.*" level="ERROR" additivity="false"/>
    <logger name="org.apache.*" level="ERROR" additivity="false"/>
    <logger name="com.zaxxer.hikari.*" level="ERROR" additivity="false"/>
    <logger name="org.redisson.*" level="ERROR" additivity="false"/>
    <logger name="com.lms.*" level="ERROR" additivity="false"/>
    <logger name="feign.*" level="ERROR" additivity="false"/>
    <logger name="com.good.beneficiary" level="ERROR" additivity="false"/>

    <!-- Suppress Lower-Level Logs Globally -->
    <logger name="TRACE" level="ERROR" additivity="false"/>
    <logger name="DEBUG" level="ERROR" additivity="false"/>
    <logger name="INFO" level="ERROR" additivity="false"/>
</configuration>
