pipeline {
    agent any
    stages {
        stage("build project") {
            steps {
                sh 'docker kill $(docker ps -qf expose=9002) 2> /dev/null || echo "No container running on port 8761"'
                echo "Java VERSION"
                sh 'java -version'
                echo "Maven VERSION"
                sh 'mvn -version'
                echo 'building project...'
                sh "mvn clean install"
               
            }
        }
        stage('build image'){
           steps{
               sh "docker build -t lmsservice/notificationservice:${BUILD_NUMBER} ."
            }
        }
        stage('deploy image'){
           steps{
               sh "docker  run -d -p 9002:9002 -t lmsservice/notificationservice:${BUILD_NUMBER} "
            }
        }
    }
}
